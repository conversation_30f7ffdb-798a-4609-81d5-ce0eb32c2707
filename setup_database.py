#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد قاعدة البيانات لنظام إدارة المدارس
Database Setup for School Management System
"""

import sys
import os
import mysql.connector
from mysql.connector import Error
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog

class DatabaseSetup:
    """فئة إعداد قاعدة البيانات"""
    
    def __init__(self):
        self.connection = None
        self.root = None
        
    def show_setup_window(self):
        """عرض نافذة إعداد قاعدة البيانات"""
        self.root = tk.Tk()
        self.root.title("إعداد قاعدة البيانات - نظام إدارة المدارس")
        self.root.geometry("500x600")
        self.root.resizable(False, False)
        
        # العنوان
        title_label = tk.Label(self.root, text="إعداد قاعدة البيانات", 
                              font=('Arial', 16, 'bold'), fg='blue')
        title_label.pack(pady=20)
        
        # إطار الإعدادات
        settings_frame = ttk.LabelFrame(self.root, text="إعدادات الاتصال", padding="20")
        settings_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # متغيرات الإدخال
        self.host_var = tk.StringVar(value="localhost")
        self.port_var = tk.StringVar(value="3306")
        self.username_var = tk.StringVar(value="root")
        self.password_var = tk.StringVar()
        self.database_var = tk.StringVar(value="school_management")
        
        # حقول الإدخال
        fields = [
            ("الخادم (Host):", self.host_var),
            ("المنفذ (Port):", self.port_var),
            ("اسم المستخدم:", self.username_var),
            ("كلمة المرور:", self.password_var),
            ("اسم قاعدة البيانات:", self.database_var)
        ]
        
        self.entries = {}
        
        for i, (label, var) in enumerate(fields):
            tk.Label(settings_frame, text=label).grid(row=i, column=0, sticky=tk.W, pady=5)
            
            if "كلمة المرور" in label:
                entry = tk.Entry(settings_frame, textvariable=var, show="*", width=30)
            else:
                entry = tk.Entry(settings_frame, textvariable=var, width=30)
            
            entry.grid(row=i, column=1, padx=(10, 0), pady=5)
            self.entries[label] = entry
        
        # أزرار العمليات
        buttons_frame = tk.Frame(self.root)
        buttons_frame.pack(pady=20)
        
        tk.Button(buttons_frame, text="اختبار الاتصال", command=self.test_connection,
                 bg='blue', fg='white', font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إنشاء قاعدة البيانات", command=self.create_database,
                 bg='green', fg='white', font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إنشاء الجداول", command=self.create_tables,
                 bg='orange', fg='white', font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=5)
        
        # منطقة السجل
        log_frame = ttk.LabelFrame(self.root, text="سجل العمليات", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        self.log_text = tk.Text(log_frame, height=10, width=60)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # زر الإغلاق
        tk.Button(self.root, text="إغلاق", command=self.root.destroy,
                 bg='red', fg='white', font=('Arial', 10, 'bold')).pack(pady=10)
        
        # رسالة ترحيبية
        self.log("مرحباً بك في معالج إعداد قاعدة البيانات")
        self.log("يرجى إدخال بيانات الاتصال بـ MySQL ثم اختبار الاتصال")
        
        # توسيط النافذة
        self.center_window()
        
        self.root.mainloop()
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def log(self, message):
        """إضافة رسالة إلى السجل"""
        self.log_text.insert(tk.END, f"[{self.get_timestamp()}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def get_timestamp(self):
        """الحصول على الوقت الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def get_connection_config(self):
        """الحصول على إعدادات الاتصال"""
        return {
            'host': self.host_var.get(),
            'port': int(self.port_var.get()),
            'user': self.username_var.get(),
            'password': self.password_var.get(),
            'charset': 'utf8mb4',
            'collation': 'utf8mb4_unicode_ci'
        }
    
    def test_connection(self):
        """اختبار الاتصال بـ MySQL"""
        self.log("اختبار الاتصال بـ MySQL...")
        
        try:
            config = self.get_connection_config()
            connection = mysql.connector.connect(**config)
            
            if connection.is_connected():
                db_info = connection.get_server_info()
                self.log(f"✓ تم الاتصال بنجاح! إصدار MySQL: {db_info}")
                connection.close()
                messagebox.showinfo("نجح", "تم الاتصال بـ MySQL بنجاح!")
                return True
            
        except Error as e:
            self.log(f"✗ خطأ في الاتصال: {e}")
            messagebox.showerror("خطأ", f"فشل الاتصال بـ MySQL:\n{e}")
            return False
    
    def create_database(self):
        """إنشاء قاعدة البيانات"""
        self.log("إنشاء قاعدة البيانات...")
        
        try:
            config = self.get_connection_config()
            connection = mysql.connector.connect(**config)
            cursor = connection.cursor()
            
            database_name = self.database_var.get()
            
            # إنشاء قاعدة البيانات
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {database_name} "
                          f"CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            
            self.log(f"✓ تم إنشاء قاعدة البيانات '{database_name}' بنجاح")
            
            cursor.close()
            connection.close()
            
            messagebox.showinfo("نجح", f"تم إنشاء قاعدة البيانات '{database_name}' بنجاح!")
            return True
            
        except Error as e:
            self.log(f"✗ خطأ في إنشاء قاعدة البيانات: {e}")
            messagebox.showerror("خطأ", f"فشل إنشاء قاعدة البيانات:\n{e}")
            return False
    
    def create_tables(self):
        """إنشاء الجداول من ملف SQL"""
        self.log("إنشاء الجداول...")
        
        sql_file = "database/school_db.sql"
        
        if not os.path.exists(sql_file):
            self.log(f"✗ ملف SQL غير موجود: {sql_file}")
            messagebox.showerror("خطأ", f"ملف SQL غير موجود:\n{sql_file}")
            return False
        
        try:
            config = self.get_connection_config()
            config['database'] = self.database_var.get()
            
            connection = mysql.connector.connect(**config)
            cursor = connection.cursor()
            
            # قراءة ملف SQL
            with open(sql_file, 'r', encoding='utf-8') as file:
                sql_script = file.read()
            
            # تقسيم الاستعلامات
            sql_commands = sql_script.split(';')
            
            executed_count = 0
            for command in sql_commands:
                command = command.strip()
                if command and not command.startswith('--'):
                    try:
                        cursor.execute(command)
                        executed_count += 1
                    except Error as e:
                        if "already exists" not in str(e).lower():
                            self.log(f"تحذير: {e}")
            
            connection.commit()
            self.log(f"✓ تم تنفيذ {executed_count} استعلام بنجاح")
            
            cursor.close()
            connection.close()
            
            messagebox.showinfo("نجح", "تم إنشاء الجداول بنجاح!")
            
            # تحديث ملف الإعدادات
            self.update_config_file()
            
            return True
            
        except Error as e:
            self.log(f"✗ خطأ في إنشاء الجداول: {e}")
            messagebox.showerror("خطأ", f"فشل إنشاء الجداول:\n{e}")
            return False
    
    def update_config_file(self):
        """تحديث ملف إعدادات قاعدة البيانات"""
        try:
            config_file = "config/database_config.py"
            
            if os.path.exists(config_file):
                # قراءة الملف الحالي
                with open(config_file, 'r', encoding='utf-8') as file:
                    content = file.read()
                
                # تحديث الإعدادات
                new_config = f"""        'host': '{self.host_var.get()}',
        'database': '{self.database_var.get()}',
        'user': '{self.username_var.get()}',
        'password': '{self.password_var.get()}',"""
                
                # استبدال الإعدادات القديمة
                import re
                pattern = r"'host':\s*'[^']*',\s*'database':\s*'[^']*',\s*'user':\s*'[^']*',\s*'password':\s*'[^']*',"
                content = re.sub(pattern, new_config, content)
                
                # كتابة الملف المحدث
                with open(config_file, 'w', encoding='utf-8') as file:
                    file.write(content)
                
                self.log("✓ تم تحديث ملف الإعدادات")
            
        except Exception as e:
            self.log(f"تحذير: لم يتم تحديث ملف الإعدادات: {e}")

def main():
    """الدالة الرئيسية"""
    print("معالج إعداد قاعدة البيانات - نظام إدارة المدارس")
    print("=" * 50)
    
    # التحقق من توفر mysql-connector-python
    try:
        import mysql.connector
    except ImportError:
        print("خطأ: mysql-connector-python غير مثبت")
        print("يرجى تثبيته باستخدام: pip install mysql-connector-python")
        return
    
    # تشغيل معالج الإعداد
    setup = DatabaseSetup()
    setup.show_setup_window()

if __name__ == "__main__":
    main()
