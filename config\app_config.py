# إعدادات التطبيق العامة
import os
from datetime import datetime

class AppConfig:
    """إعدادات التطبيق الرئيسية"""
    
    # معلومات التطبيق
    APP_NAME = "نظام إدارة المدارس"
    APP_VERSION = "1.0.0"
    APP_AUTHOR = "فريق التطوير"
    
    # إعدادات النافذة الرئيسية
    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800
    WINDOW_MIN_WIDTH = 1000
    WINDOW_MIN_HEIGHT = 600
    
    # الألوان والتصميم
    COLORS = {
        'primary': '#2E86AB',      # أزرق رئيسي
        'secondary': '#A23B72',    # وردي ثانوي
        'success': '#28A745',      # أخضر للنجاح
        'warning': '#FFC107',      # أصفر للتحذير
        'danger': '#DC3545',       # أحمر للخطر
        'info': '#17A2B8',         # أزرق فاتح للمعلومات
        'light': '#F8F9FA',        # رمادي فاتح
        'dark': '#343A40',         # رمادي داكن
        'white': '#FFFFFF',        # أبيض
        'background': '#F5F5F5'    # خلفية رمادية فاتحة
    }
    
    # الخطوط
    FONTS = {
        'default': ('Arial', 10),
        'heading': ('Arial', 14, 'bold'),
        'title': ('Arial', 16, 'bold'),
        'small': ('Arial', 8),
        'button': ('Arial', 10, 'bold')
    }
    
    # اللغات المدعومة
    SUPPORTED_LANGUAGES = {
        'ar': 'العربية',
        'fr': 'Français'
    }
    
    # اللغة الافتراضية
    DEFAULT_LANGUAGE = 'ar'
    
    # مسارات الملفات
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    ASSETS_DIR = os.path.join(BASE_DIR, 'assets')
    IMAGES_DIR = os.path.join(ASSETS_DIR, 'images')
    ICONS_DIR = os.path.join(ASSETS_DIR, 'icons')
    REPORTS_DIR = os.path.join(BASE_DIR, 'reports')
    BACKUPS_DIR = os.path.join(BASE_DIR, 'backups')
    LOGS_DIR = os.path.join(BASE_DIR, 'logs')
    
    # إعدادات التقارير
    REPORT_FORMATS = ['PDF', 'Excel', 'CSV']
    DEFAULT_REPORT_FORMAT = 'PDF'
    
    # إعدادات البريد الإلكتروني
    EMAIL_CONFIG = {
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'use_tls': True,
        'sender_email': '',  # يجب تعبئتها
        'sender_password': ''  # يجب تعبئتها
    }
    
    # إعدادات الأمان
    SECURITY_CONFIG = {
        'password_min_length': 6,
        'password_require_uppercase': False,
        'password_require_lowercase': False,
        'password_require_numbers': True,
        'password_require_symbols': False,
        'session_timeout_minutes': 60,
        'max_login_attempts': 3,
        'lockout_duration_minutes': 15
    }
    
    # إعدادات النسخ الاحتياطي
    BACKUP_CONFIG = {
        'auto_backup': True,
        'backup_interval_days': 7,
        'max_backup_files': 10,
        'backup_time': '02:00'  # 2:00 AM
    }
    
    # السنة الأكاديمية الحالية
    @staticmethod
    def get_current_academic_year():
        """الحصول على السنة الأكاديمية الحالية"""
        current_year = datetime.now().year
        current_month = datetime.now().month
        
        # إذا كان الشهر من سبتمبر إلى ديسمبر، فالسنة الأكاديمية تبدأ من السنة الحالية
        if current_month >= 9:
            return f"{current_year}-{current_year + 1}"
        else:
            return f"{current_year - 1}-{current_year}"
    
    # إنشاء المجلدات المطلوبة
    @staticmethod
    def create_directories():
        """إنشاء المجلدات المطلوبة للتطبيق"""
        directories = [
            AppConfig.ASSETS_DIR,
            AppConfig.IMAGES_DIR,
            AppConfig.ICONS_DIR,
            AppConfig.REPORTS_DIR,
            AppConfig.BACKUPS_DIR,
            AppConfig.LOGS_DIR
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"تم إنشاء المجلد: {directory}")

# إعدادات الترجمة
class LanguageConfig:
    """إعدادات اللغات والترجمة"""
    
    TRANSLATIONS = {
        'ar': {
            # القوائم الرئيسية
            'file': 'ملف',
            'edit': 'تحرير',
            'view': 'عرض',
            'tools': 'أدوات',
            'help': 'مساعدة',
            
            # الأزرار العامة
            'add': 'إضافة',
            'edit': 'تعديل',
            'delete': 'حذف',
            'save': 'حفظ',
            'cancel': 'إلغاء',
            'search': 'بحث',
            'refresh': 'تحديث',
            'print': 'طباعة',
            'export': 'تصدير',
            'import': 'استيراد',
            
            # الرسائل
            'success': 'تم بنجاح',
            'error': 'خطأ',
            'warning': 'تحذير',
            'info': 'معلومات',
            'confirm': 'تأكيد',
            'yes': 'نعم',
            'no': 'لا',
            
            # الوحدات الرئيسية
            'students': 'الطلاب',
            'teachers': 'المعلمون',
            'classes': 'الصفوف',
            'subjects': 'المواد',
            'schedules': 'الجداول',
            'attendance': 'الحضور والغياب',
            'grades': 'الدرجات',
            'exams': 'الامتحانات',
            'invoices': 'الفواتير',
            'payments': 'المدفوعات',
            'notifications': 'الإشعارات',
            'reports': 'التقارير',
            'settings': 'الإعدادات'
        },
        'fr': {
            # القوائم الرئيسية
            'file': 'Fichier',
            'edit': 'Éditer',
            'view': 'Affichage',
            'tools': 'Outils',
            'help': 'Aide',
            
            # الأزرار العامة
            'add': 'Ajouter',
            'edit': 'Modifier',
            'delete': 'Supprimer',
            'save': 'Enregistrer',
            'cancel': 'Annuler',
            'search': 'Rechercher',
            'refresh': 'Actualiser',
            'print': 'Imprimer',
            'export': 'Exporter',
            'import': 'Importer',
            
            # الرسائل
            'success': 'Succès',
            'error': 'Erreur',
            'warning': 'Avertissement',
            'info': 'Information',
            'confirm': 'Confirmer',
            'yes': 'Oui',
            'no': 'Non',
            
            # الوحدات الرئيسية
            'students': 'Étudiants',
            'teachers': 'Enseignants',
            'classes': 'Classes',
            'subjects': 'Matières',
            'schedules': 'Horaires',
            'attendance': 'Présence',
            'grades': 'Notes',
            'exams': 'Examens',
            'invoices': 'Factures',
            'payments': 'Paiements',
            'notifications': 'Notifications',
            'reports': 'Rapports',
            'settings': 'Paramètres'
        }
    }
    
    @staticmethod
    def get_text(key, language='ar'):
        """الحصول على النص المترجم"""
        return LanguageConfig.TRANSLATIONS.get(language, {}).get(key, key)
