# النافذة الرئيسية للتطبيق
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import os

from config.app_config import AppConfig, LanguageConfig
from utils.security import Permission<PERSON><PERSON><PERSON>, session_manager
from gui.students_window import StudentsWindow

class MainWindow:
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self, parent, user_data, on_logout_callback):
        self.parent = parent
        self.user_data = user_data
        self.on_logout_callback = on_logout_callback
        self.window = None
        self.current_language = AppConfig.DEFAULT_LANGUAGE
        
        # الوحدات المحملة
        self.loaded_modules = {}
        
    def show(self):
        """عرض النافذة الرئيسية"""
        self.window = tk.Toplevel(self.parent)
        self.setup_window()
        self.create_menu_bar()
        self.create_main_layout()
        self.load_dashboard()
        
        # إظهار النافذة
        self.window.deiconify()
        self.center_window()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.window.title(f"{AppConfig.APP_NAME} - {self.user_data['full_name']}")
        self.window.geometry(f"{AppConfig.WINDOW_WIDTH}x{AppConfig.WINDOW_HEIGHT}")
        self.window.minsize(AppConfig.WINDOW_MIN_WIDTH, AppConfig.WINDOW_MIN_HEIGHT)
        self.window.configure(bg=AppConfig.COLORS['background'])
        
        # معالج إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="إعدادات", command=self.show_settings)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.on_close)
        
        # قائمة الطلاب
        if PermissionManager.has_permission(self.user_data['user_type'], 'manage_students'):
            students_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="الطلاب", menu=students_menu)
            students_menu.add_command(label="إدارة الطلاب", command=lambda: self.load_module('students'))
            students_menu.add_command(label="تسجيل طالب جديد", command=lambda: self.load_module('add_student'))
        
        # قائمة المعلمين
        if PermissionManager.has_permission(self.user_data['user_type'], 'manage_teachers'):
            teachers_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="المعلمون", menu=teachers_menu)
            teachers_menu.add_command(label="إدارة المعلمين", command=lambda: self.load_module('teachers'))
            teachers_menu.add_command(label="إضافة معلم جديد", command=lambda: self.load_module('add_teacher'))
        
        # قائمة الصفوف والجداول
        if PermissionManager.has_permission(self.user_data['user_type'], 'manage_schedules'):
            classes_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="الصفوف والجداول", menu=classes_menu)
            classes_menu.add_command(label="إدارة الصفوف", command=lambda: self.load_module('classes'))
            classes_menu.add_command(label="الجداول الدراسية", command=lambda: self.load_module('schedules'))
            classes_menu.add_command(label="المواد الدراسية", command=lambda: self.load_module('subjects'))
        
        # قائمة الحضور والغياب
        if PermissionManager.has_permission(self.user_data['user_type'], 'manage_attendance'):
            attendance_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="الحضور والغياب", menu=attendance_menu)
            attendance_menu.add_command(label="تسجيل الحضور", command=lambda: self.load_module('attendance'))
            attendance_menu.add_command(label="تقارير الحضور", command=lambda: self.load_module('attendance_reports'))
        
        # قائمة الدرجات والامتحانات
        if PermissionManager.has_permission(self.user_data['user_type'], 'manage_grades'):
            grades_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="الدرجات والامتحانات", menu=grades_menu)
            grades_menu.add_command(label="إدارة الامتحانات", command=lambda: self.load_module('exams'))
            grades_menu.add_command(label="إدخال الدرجات", command=lambda: self.load_module('grades'))
            grades_menu.add_command(label="تقارير الدرجات", command=lambda: self.load_module('grade_reports'))
        
        # قائمة المالية
        if PermissionManager.has_permission(self.user_data['user_type'], 'manage_invoices'):
            finance_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="المالية", menu=finance_menu)
            finance_menu.add_command(label="الفواتير", command=lambda: self.load_module('invoices'))
            finance_menu.add_command(label="المدفوعات", command=lambda: self.load_module('payments'))
            finance_menu.add_command(label="التقارير المالية", command=lambda: self.load_module('financial_reports'))
        
        # قائمة التقارير
        if PermissionManager.has_permission(self.user_data['user_type'], 'view_reports'):
            reports_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="التقارير", menu=reports_menu)
            reports_menu.add_command(label="تقرير شامل", command=lambda: self.load_module('comprehensive_report'))
            reports_menu.add_command(label="إحصائيات", command=lambda: self.load_module('statistics'))
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_user_guide)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_main_layout(self):
        """إنشاء التخطيط الرئيسي"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # شريط الحالة العلوي
        self.create_status_bar(main_frame)
        
        # الإطار الجانبي والمحتوى
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # الشريط الجانبي
        self.create_sidebar(content_frame)
        
        # منطقة المحتوى الرئيسي
        self.content_area = ttk.Frame(content_frame)
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # شريط الحالة السفلي
        self.create_bottom_status_bar(main_frame)
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة العلوي"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        # معلومات المستخدم
        user_info = f"مرحباً، {self.user_data['full_name']} ({self.get_user_type_text()})"
        user_label = ttk.Label(status_frame, text=user_info, style='Heading.TLabel')
        user_label.pack(side=tk.LEFT)
        
        # التاريخ والوقت
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        time_label = ttk.Label(status_frame, text=current_time)
        time_label.pack(side=tk.RIGHT)
        
        # تحديث الوقت كل دقيقة
        self.update_time(time_label)
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        sidebar = ttk.LabelFrame(parent, text="القوائم الرئيسية", width=200)
        sidebar.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        sidebar.pack_propagate(False)
        
        # أزرار القوائم الرئيسية
        buttons_data = [
            ("🏠", "الرئيسية", "dashboard"),
            ("👥", "الطلاب", "students") if PermissionManager.has_permission(self.user_data['user_type'], 'manage_students') else None,
            ("👨‍🏫", "المعلمون", "teachers") if PermissionManager.has_permission(self.user_data['user_type'], 'manage_teachers') else None,
            ("📚", "الصفوف", "classes") if PermissionManager.has_permission(self.user_data['user_type'], 'manage_classes') else None,
            ("📅", "الجداول", "schedules") if PermissionManager.has_permission(self.user_data['user_type'], 'manage_schedules') else None,
            ("✅", "الحضور", "attendance") if PermissionManager.has_permission(self.user_data['user_type'], 'manage_attendance') else None,
            ("📊", "الدرجات", "grades") if PermissionManager.has_permission(self.user_data['user_type'], 'manage_grades') else None,
            ("💰", "المالية", "invoices") if PermissionManager.has_permission(self.user_data['user_type'], 'manage_invoices') else None,
            ("📈", "التقارير", "reports") if PermissionManager.has_permission(self.user_data['user_type'], 'view_reports') else None,
        ]
        
        # إزالة العناصر الفارغة
        buttons_data = [btn for btn in buttons_data if btn is not None]
        
        for icon, text, module in buttons_data:
            btn = ttk.Button(sidebar,
                           text=f"{icon} {text}",
                           command=lambda m=module: self.load_module(m),
                           width=20)
            btn.pack(fill=tk.X, pady=2, padx=5)
    
    def create_bottom_status_bar(self, parent):
        """إنشاء شريط الحالة السفلي"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))
        
        # معلومات النظام
        system_info = f"{AppConfig.APP_NAME} v{AppConfig.APP_VERSION}"
        ttk.Label(status_frame, text=system_info, font=AppConfig.FONTS['small']).pack(side=tk.LEFT)
        
        # السنة الأكاديمية
        academic_year = AppConfig.get_current_academic_year()
        ttk.Label(status_frame, text=f"السنة الأكاديمية: {academic_year}", 
                 font=AppConfig.FONTS['small']).pack(side=tk.RIGHT)
    
    def load_dashboard(self):
        """تحميل لوحة التحكم الرئيسية"""
        # مسح المحتوى الحالي
        for widget in self.content_area.winfo_children():
            widget.destroy()
        
        # إنشاء لوحة التحكم
        dashboard_frame = ttk.Frame(self.content_area)
        dashboard_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان لوحة التحكم
        title_label = ttk.Label(dashboard_frame, text="لوحة التحكم الرئيسية", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # إحصائيات سريعة
        self.create_quick_stats(dashboard_frame)
        
        # الإشعارات الحديثة
        self.create_recent_notifications(dashboard_frame)
        
        # الأنشطة الحديثة
        self.create_recent_activities(dashboard_frame)
    
    def create_quick_stats(self, parent):
        """إنشاء الإحصائيات السريعة"""
        stats_frame = ttk.LabelFrame(parent, text="إحصائيات سريعة")
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        # إطار الإحصائيات
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X, padx=10, pady=10)
        
        # بيانات وهمية للإحصائيات (ستحتاج لاستبدالها ببيانات حقيقية)
        stats_data = [
            ("👥", "إجمالي الطلاب", "1,250"),
            ("👨‍🏫", "إجمالي المعلمين", "85"),
            ("📚", "الصفوف النشطة", "42"),
            ("💰", "الفواتير المعلقة", "15")
        ]
        
        for i, (icon, label, value) in enumerate(stats_data):
            col = i % 4
            
            stat_frame = ttk.Frame(stats_grid)
            stat_frame.grid(row=0, column=col, padx=10, pady=5, sticky="ew")
            
            ttk.Label(stat_frame, text=icon, font=('Arial', 24)).pack()
            ttk.Label(stat_frame, text=label, font=AppConfig.FONTS['small']).pack()
            ttk.Label(stat_frame, text=value, font=AppConfig.FONTS['heading']).pack()
        
        # تكوين الأعمدة
        for i in range(4):
            stats_grid.columnconfigure(i, weight=1)
    
    def create_recent_notifications(self, parent):
        """إنشاء الإشعارات الحديثة"""
        notifications_frame = ttk.LabelFrame(parent, text="الإشعارات الحديثة")
        notifications_frame.pack(fill=tk.X, pady=(0, 20))
        
        # قائمة الإشعارات (بيانات وهمية)
        notifications = [
            "تم تسجيل 5 طلاب جدد اليوم",
            "امتحان الرياضيات للصف الثالث غداً",
            "اجتماع المعلمين يوم الخميس الساعة 2:00 م"
        ]
        
        for notification in notifications:
            ttk.Label(notifications_frame, text=f"• {notification}").pack(anchor=tk.W, padx=10, pady=2)
    
    def create_recent_activities(self, parent):
        """إنشاء الأنشطة الحديثة"""
        activities_frame = ttk.LabelFrame(parent, text="الأنشطة الحديثة")
        activities_frame.pack(fill=tk.BOTH, expand=True)
        
        # قائمة الأنشطة (بيانات وهمية)
        activities = [
            "تم إضافة طالب جديد: أحمد محمد",
            "تم تحديث درجات امتحان العلوم",
            "تم إرسال إشعار لأولياء الأمور"
        ]
        
        for activity in activities:
            ttk.Label(activities_frame, text=f"• {activity}").pack(anchor=tk.W, padx=10, pady=2)
    
    def load_module(self, module_name):
        """تحميل وحدة معينة"""
        # مسح المحتوى الحالي
        for widget in self.content_area.winfo_children():
            widget.destroy()

        if module_name == "dashboard":
            self.load_dashboard()
        elif module_name == "students":
            self.load_students_module()
        else:
            # عرض رسالة مؤقتة للوحدات غير المطورة بعد
            temp_frame = ttk.Frame(self.content_area)
            temp_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            ttk.Label(temp_frame,
                     text=f"وحدة {module_name} قيد التطوير",
                     style='Title.TLabel').pack(expand=True)

    def load_students_module(self):
        """تحميل وحدة إدارة الطلاب"""
        students_window = StudentsWindow(self.window)
        students_window.show()
    
    def get_user_type_text(self):
        """الحصول على نص نوع المستخدم"""
        user_types = {
            'admin': 'مدير النظام',
            'teacher': 'معلم',
            'student': 'طالب',
            'parent': 'ولي أمر'
        }
        return user_types.get(self.user_data['user_type'], 'مستخدم')
    
    def update_time(self, time_label):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        time_label.config(text=current_time)
        # تحديث كل دقيقة
        self.window.after(60000, lambda: self.update_time(time_label))
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def show_settings(self):
        """عرض نافذة الإعدادات"""
        messagebox.showinfo("الإعدادات", "نافذة الإعدادات قيد التطوير")
    
    def show_user_guide(self):
        """عرض دليل المستخدم"""
        messagebox.showinfo("دليل المستخدم", "دليل المستخدم قيد الإعداد")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = f"""
{AppConfig.APP_NAME}
الإصدار: {AppConfig.APP_VERSION}
المطور: {AppConfig.APP_AUTHOR}

نظام شامل لإدارة المدارس يشمل:
• إدارة الطلاب والمعلمين
• الجداول الدراسية
• الحضور والغياب
• الدرجات والامتحانات
• الفواتير والمدفوعات
• التقارير والإحصائيات
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج؟")
        if result:
            self.window.destroy()
            self.on_logout_callback()
    
    def on_close(self):
        """معالج إغلاق النافذة"""
        result = messagebox.askyesno("إغلاق البرنامج", "هل تريد إغلاق البرنامج؟")
        if result:
            self.parent.quit()
    
    def update_language(self, language):
        """تحديث لغة الواجهة"""
        self.current_language = language
        # إعادة إنشاء الواجهة باللغة الجديدة
        pass
    
    def destroy(self):
        """تدمير النافذة"""
        if self.window:
            self.window.destroy()
