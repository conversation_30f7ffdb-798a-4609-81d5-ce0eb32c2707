#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد نظام إدارة المدارس
School Management System Setup
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

class SystemSetup:
    """فئة إعداد النظام"""
    
    def __init__(self):
        self.system = platform.system()
        self.python_version = sys.version_info
        self.project_root = Path(__file__).parent
        
    def print_header(self):
        """طباعة رأس الإعداد"""
        print("=" * 70)
        print("🏫 نظام إدارة المدارس - معالج الإعداد")
        print("   School Management System - Setup Wizard")
        print("=" * 70)
        print(f"نظام التشغيل: {self.system}")
        print(f"إصدار Python: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}")
        print("=" * 70)
    
    def check_python_version(self):
        """فحص إصدار Python"""
        print("🔍 فحص إصدار Python...")
        
        if self.python_version < (3, 8):
            print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
            print(f"   الإصدار الحالي: {self.python_version.major}.{self.python_version.minor}")
            print("   يرجى تحديث Python من: https://www.python.org/downloads/")
            return False
        
        print(f"✅ إصدار Python مناسب: {self.python_version.major}.{self.python_version.minor}")
        return True
    
    def check_pip(self):
        """فحص pip"""
        print("🔍 فحص pip...")
        
        try:
            import pip
            print("✅ pip متوفر")
            return True
        except ImportError:
            print("❌ pip غير متوفر")
            print("   يرجى تثبيت pip أولاً")
            return False
    
    def create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        print("📁 إنشاء المجلدات...")
        
        directories = [
            'logs',
            'backups',
            'exports',
            'temp',
            'docs/images',
            'docs/reports'
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"   ✅ {directory}")
        
        print("✅ تم إنشاء جميع المجلدات")
    
    def install_requirements(self):
        """تثبيت المتطلبات"""
        print("📦 تثبيت المتطلبات...")
        
        requirements_file = self.project_root / 'requirements.txt'
        
        if not requirements_file.exists():
            print("❌ ملف requirements.txt غير موجود")
            return False
        
        try:
            # تحديث pip أولاً
            print("   🔄 تحديث pip...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
            
            # تثبيت المتطلبات
            print("   📦 تثبيت المكتبات...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)])
            
            print("✅ تم تثبيت جميع المتطلبات")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ في تثبيت المتطلبات: {e}")
            return False
    
    def check_mysql(self):
        """فحص MySQL"""
        print("🔍 فحص MySQL...")
        
        try:
            import mysql.connector
            print("✅ MySQL Connector متوفر")
            
            # محاولة الاتصال (اختياري)
            try:
                from config.database_config import DatabaseConfig
                if DatabaseConfig.test_connection():
                    print("✅ الاتصال بـ MySQL ناجح")
                else:
                    print("⚠️  لا يمكن الاتصال بـ MySQL (يمكن إعداده لاحقاً)")
            except:
                print("⚠️  إعدادات MySQL غير مكتملة (يمكن إعداده لاحقاً)")
            
            return True
            
        except ImportError:
            print("❌ MySQL Connector غير متوفر")
            print("   سيتم تثبيته مع المتطلبات")
            return False
    
    def create_config_files(self):
        """إنشاء ملفات الإعدادات"""
        print("⚙️  إنشاء ملفات الإعدادات...")
        
        # إنشاء ملف إعدادات محلي
        local_config = self.project_root / 'config' / 'local_config.py'
        
        if not local_config.exists():
            config_content = '''# -*- coding: utf-8 -*-
"""
إعدادات محلية - يمكن تخصيصها حسب البيئة
Local Configuration - Can be customized per environment
"""

# إعدادات قاعدة البيانات المحلية
LOCAL_DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'school_management',
    'user': 'root',
    'password': '',  # أدخل كلمة المرور هنا
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci'
}

# إعدادات التطبيق المحلية
LOCAL_APP_CONFIG = {
    'debug_mode': True,
    'log_level': 'INFO',
    'backup_enabled': True,
    'backup_interval': 24,  # ساعات
    'max_login_attempts': 3,
    'session_timeout': 30,  # دقائق
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'password_min_length': 8,
    'password_require_uppercase': True,
    'password_require_lowercase': True,
    'password_require_numbers': True,
    'password_require_symbols': False,
    'session_encryption': True
}
'''
            
            with open(local_config, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            print("   ✅ local_config.py")
        
        # إنشاء ملف سجل الأخطاء
        log_config = self.project_root / 'logs' / '.gitkeep'
        log_config.touch()
        
        print("✅ تم إنشاء ملفات الإعدادات")
    
    def create_shortcuts(self):
        """إنشاء اختصارات التشغيل"""
        print("🔗 إنشاء اختصارات التشغيل...")
        
        # اختصار Windows
        if self.system == "Windows":
            batch_content = f'''@echo off
cd /d "{self.project_root}"
python main.py
pause
'''
            with open(self.project_root / 'تشغيل_النظام.bat', 'w', encoding='utf-8') as f:
                f.write(batch_content)
            print("   ✅ تشغيل_النظام.bat")
        
        # اختصار Linux/Mac
        else:
            shell_content = f'''#!/bin/bash
cd "{self.project_root}"
python3 main.py
'''
            script_path = self.project_root / 'run_system.sh'
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(shell_content)
            
            # جعل الملف قابل للتنفيذ
            os.chmod(script_path, 0o755)
            print("   ✅ run_system.sh")
        
        print("✅ تم إنشاء اختصارات التشغيل")
    
    def run_tests(self):
        """تشغيل الاختبارات"""
        print("🧪 تشغيل اختبارات النظام...")
        
        try:
            # تشغيل اختبارات بسيطة
            test_file = self.project_root / 'test_system.py'
            if test_file.exists():
                result = subprocess.run([sys.executable, str(test_file)], 
                                      capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    print("✅ جميع الاختبارات نجحت")
                    return True
                else:
                    print("⚠️  بعض الاختبارات فشلت (يمكن المتابعة)")
                    print(f"   التفاصيل: {result.stderr[:200]}...")
                    return True
            else:
                print("⚠️  ملف الاختبارات غير موجود")
                return True
                
        except Exception as e:
            print(f"⚠️  خطأ في تشغيل الاختبارات: {e}")
            return True  # لا نوقف الإعداد بسبب فشل الاختبارات
    
    def print_completion_message(self):
        """طباعة رسالة الإكمال"""
        print("\n" + "=" * 70)
        print("🎉 تم إكمال إعداد النظام بنجاح!")
        print("   System setup completed successfully!")
        print("=" * 70)
        
        print("\n📋 الخطوات التالية:")
        print("1. قم بإعداد قاعدة البيانات:")
        print("   python setup_database.py")
        print("\n2. شغل النظام:")
        print("   python main.py")
        print("\n3. سجل دخول بحساب المدير:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        
        print("\n📚 للمساعدة:")
        print("- دليل التثبيت: INSTALLATION_GUIDE.md")
        print("- دليل المستخدم: USER_GUIDE.md")
        print("- اختبار النظام: python test_system.py")
        
        print("\n" + "=" * 70)
    
    def run_setup(self):
        """تشغيل عملية الإعداد الكاملة"""
        self.print_header()
        
        steps = [
            ("فحص إصدار Python", self.check_python_version),
            ("فحص pip", self.check_pip),
            ("إنشاء المجلدات", self.create_directories),
            ("تثبيت المتطلبات", self.install_requirements),
            ("فحص MySQL", self.check_mysql),
            ("إنشاء ملفات الإعدادات", self.create_config_files),
            ("إنشاء اختصارات التشغيل", self.create_shortcuts),
            ("تشغيل الاختبارات", self.run_tests)
        ]
        
        failed_steps = []
        
        for step_name, step_function in steps:
            print(f"\n🔄 {step_name}...")
            try:
                if not step_function():
                    failed_steps.append(step_name)
            except Exception as e:
                print(f"❌ خطأ في {step_name}: {e}")
                failed_steps.append(step_name)
        
        if failed_steps:
            print(f"\n⚠️  فشلت الخطوات التالية: {', '.join(failed_steps)}")
            print("   يمكنك المتابعة أو إعادة تشغيل الإعداد")
        
        self.print_completion_message()

def main():
    """الدالة الرئيسية"""
    setup = SystemSetup()
    setup.run_setup()

if __name__ == "__main__":
    main()
