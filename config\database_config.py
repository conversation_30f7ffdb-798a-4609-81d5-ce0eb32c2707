# إعدادات قاعدة البيانات
import mysql.connector
from mysql.connector import Error
import os

class DatabaseConfig:
    """فئة إعدادات قاعدة البيانات"""
    
    # إعدادات الاتصال بقاعدة البيانات
    DB_CONFIG = {
        'host': 'localhost',
        'database': 'school_management',
        'user': 'root',
        'password': '',  # يجب تغييرها حسب إعدادات MySQL المحلية
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci',
        'autocommit': True
    }
    
    @staticmethod
    def get_connection():
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            connection = mysql.connector.connect(**DatabaseConfig.DB_CONFIG)
            if connection.is_connected():
                return connection
        except Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return None
    
    @staticmethod
    def test_connection():
        """اختبار الاتصال بقاعدة البيانات"""
        connection = DatabaseConfig.get_connection()
        if connection:
            try:
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                connection.close()
                return True
            except Error as e:
                print(f"خطأ في اختبار الاتصال: {e}")
                return False
        return False
    
    @staticmethod
    def create_database():
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        try:
            # الاتصال بدون تحديد قاعدة بيانات
            temp_config = DatabaseConfig.DB_CONFIG.copy()
            del temp_config['database']
            
            connection = mysql.connector.connect(**temp_config)
            cursor = connection.cursor()
            
            # إنشاء قاعدة البيانات
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DatabaseConfig.DB_CONFIG['database']} "
                          f"CHARACTER SET {DatabaseConfig.DB_CONFIG['charset']} "
                          f"COLLATE {DatabaseConfig.DB_CONFIG['collation']}")
            
            cursor.close()
            connection.close()
            print("تم إنشاء قاعدة البيانات بنجاح")
            return True
            
        except Error as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    
    @staticmethod
    def execute_sql_file(file_path):
        """تنفيذ ملف SQL"""
        try:
            connection = DatabaseConfig.get_connection()
            if not connection:
                return False
                
            cursor = connection.cursor()
            
            # قراءة ملف SQL
            with open(file_path, 'r', encoding='utf-8') as file:
                sql_script = file.read()
            
            # تقسيم الاستعلامات
            sql_commands = sql_script.split(';')
            
            for command in sql_commands:
                command = command.strip()
                if command:
                    cursor.execute(command)
            
            connection.commit()
            cursor.close()
            connection.close()
            print(f"تم تنفيذ ملف SQL بنجاح: {file_path}")
            return True
            
        except Error as e:
            print(f"خطأ في تنفيذ ملف SQL: {e}")
            return False
        except FileNotFoundError:
            print(f"لم يتم العثور على الملف: {file_path}")
            return False

# فئة قاعدة البيانات الأساسية
class Database:
    """فئة أساسية لعمليات قاعدة البيانات"""
    
    def __init__(self):
        self.connection = None
        self.cursor = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        self.connection = DatabaseConfig.get_connection()
        if self.connection:
            self.cursor = self.connection.cursor(dictionary=True)
            return True
        return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام SELECT"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect():
                    return None
            
            self.cursor.execute(query, params or ())
            return self.cursor.fetchall()
        except Error as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return None
    
    def execute_update(self, query, params=None):
        """تنفيذ استعلام INSERT/UPDATE/DELETE"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect():
                    return False
            
            self.cursor.execute(query, params or ())
            self.connection.commit()
            return True
        except Error as e:
            print(f"خطأ في تنفيذ التحديث: {e}")
            self.connection.rollback()
            return False
    
    def get_last_insert_id(self):
        """الحصول على آخر ID تم إدراجه"""
        return self.cursor.lastrowid if self.cursor else None
