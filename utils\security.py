# وحدة الأمان والتشفير
import hashlib
import uuid
import secrets
import re
from datetime import datetime, timedelta
from config.app_config import AppConfig

class SecurityManager:
    """مدير الأمان والتشفير"""
    
    @staticmethod
    def hash_password(password):
        """تشفير كلمة المرور"""
        # إضافة salt عشوائي
        salt = secrets.token_hex(16)
        # تشفير كلمة المرور مع الـ salt
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return f"{salt}:{password_hash}"
    
    @staticmethod
    def verify_password(password, stored_hash):
        """التحقق من كلمة المرور"""
        try:
            salt, password_hash = stored_hash.split(':')
            # تشفير كلمة المرور المدخلة مع نفس الـ salt
            input_hash = hashlib.sha256((password + salt).encode()).hexdigest()
            return input_hash == password_hash
        except ValueError:
            # للتوافق مع كلمات المرور القديمة المشفرة بـ SHA256 فقط
            return hashlib.sha256(password.encode()).hexdigest() == stored_hash
    
    @staticmethod
    def validate_password(password):
        """التحقق من قوة كلمة المرور"""
        config = AppConfig.SECURITY_CONFIG
        errors = []
        
        # طول كلمة المرور
        if len(password) < config['password_min_length']:
            errors.append(f"كلمة المرور يجب أن تكون {config['password_min_length']} أحرف على الأقل")
        
        # الأحرف الكبيرة
        if config['password_require_uppercase'] and not re.search(r'[A-Z]', password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل")
        
        # الأحرف الصغيرة
        if config['password_require_lowercase'] and not re.search(r'[a-z]', password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل")
        
        # الأرقام
        if config['password_require_numbers'] and not re.search(r'\d', password):
            errors.append("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل")
        
        # الرموز الخاصة
        if config['password_require_symbols'] and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def generate_session_token():
        """إنشاء رمز جلسة عشوائي"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def generate_user_id():
        """إنشاء معرف مستخدم فريد"""
        return str(uuid.uuid4())

class SessionManager:
    """مدير الجلسات"""
    
    def __init__(self):
        self.active_sessions = {}
        self.login_attempts = {}
    
    def create_session(self, user_id, user_type, username):
        """إنشاء جلسة جديدة"""
        session_token = SecurityManager.generate_session_token()
        session_data = {
            'user_id': user_id,
            'user_type': user_type,
            'username': username,
            'login_time': datetime.now(),
            'last_activity': datetime.now(),
            'is_active': True
        }
        self.active_sessions[session_token] = session_data
        return session_token
    
    def validate_session(self, session_token):
        """التحقق من صحة الجلسة"""
        if session_token not in self.active_sessions:
            return False, "جلسة غير صالحة"
        
        session = self.active_sessions[session_token]
        
        # التحقق من انتهاء صلاحية الجلسة
        timeout_minutes = AppConfig.SECURITY_CONFIG['session_timeout_minutes']
        if datetime.now() - session['last_activity'] > timedelta(minutes=timeout_minutes):
            self.end_session(session_token)
            return False, "انتهت صلاحية الجلسة"
        
        # تحديث آخر نشاط
        session['last_activity'] = datetime.now()
        return True, session
    
    def end_session(self, session_token):
        """إنهاء الجلسة"""
        if session_token in self.active_sessions:
            self.active_sessions[session_token]['is_active'] = False
            del self.active_sessions[session_token]
    
    def record_login_attempt(self, username, success=False):
        """تسجيل محاولة تسجيل الدخول"""
        if username not in self.login_attempts:
            self.login_attempts[username] = {
                'attempts': 0,
                'last_attempt': None,
                'locked_until': None
            }
        
        attempt_data = self.login_attempts[username]
        attempt_data['last_attempt'] = datetime.now()
        
        if success:
            # إعادة تعيين المحاولات عند النجاح
            attempt_data['attempts'] = 0
            attempt_data['locked_until'] = None
        else:
            attempt_data['attempts'] += 1
            
            # قفل الحساب إذا تجاوز الحد الأقصى للمحاولات
            max_attempts = AppConfig.SECURITY_CONFIG['max_login_attempts']
            if attempt_data['attempts'] >= max_attempts:
                lockout_minutes = AppConfig.SECURITY_CONFIG['lockout_duration_minutes']
                attempt_data['locked_until'] = datetime.now() + timedelta(minutes=lockout_minutes)
    
    def is_account_locked(self, username):
        """التحقق من قفل الحساب"""
        if username not in self.login_attempts:
            return False, None
        
        attempt_data = self.login_attempts[username]
        locked_until = attempt_data.get('locked_until')
        
        if locked_until and datetime.now() < locked_until:
            remaining_time = locked_until - datetime.now()
            return True, remaining_time
        
        return False, None
    
    def get_active_sessions(self):
        """الحصول على الجلسات النشطة"""
        active = []
        for token, session in self.active_sessions.items():
            if session['is_active']:
                active.append({
                    'token': token,
                    'username': session['username'],
                    'user_type': session['user_type'],
                    'login_time': session['login_time'],
                    'last_activity': session['last_activity']
                })
        return active

class PermissionManager:
    """مدير الصلاحيات"""
    
    # تعريف الصلاحيات لكل نوع مستخدم
    PERMISSIONS = {
        'admin': [
            'manage_users', 'manage_students', 'manage_teachers', 'manage_classes',
            'manage_subjects', 'manage_schedules', 'manage_attendance', 'manage_grades',
            'manage_exams', 'manage_invoices', 'manage_payments', 'view_reports',
            'manage_notifications', 'manage_settings', 'backup_restore'
        ],
        'teacher': [
            'view_students', 'manage_attendance', 'manage_grades', 'manage_exams',
            'view_schedules', 'send_notifications', 'view_reports'
        ],
        'student': [
            'view_grades', 'view_attendance', 'view_schedules', 'view_notifications'
        ],
        'parent': [
            'view_student_grades', 'view_student_attendance', 'view_student_schedules',
            'view_invoices', 'view_notifications'
        ]
    }
    
    @staticmethod
    def has_permission(user_type, permission):
        """التحقق من وجود صلاحية"""
        user_permissions = PermissionManager.PERMISSIONS.get(user_type, [])
        return permission in user_permissions
    
    @staticmethod
    def get_user_permissions(user_type):
        """الحصول على صلاحيات المستخدم"""
        return PermissionManager.PERMISSIONS.get(user_type, [])
    
    @staticmethod
    def require_permission(user_type, permission):
        """ديكوريتر للتحقق من الصلاحية"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                if PermissionManager.has_permission(user_type, permission):
                    return func(*args, **kwargs)
                else:
                    raise PermissionError(f"ليس لديك صلاحية للوصول إلى: {permission}")
            return wrapper
        return decorator

# إنشاء مثيل عام لمدير الجلسات
session_manager = SessionManager()
