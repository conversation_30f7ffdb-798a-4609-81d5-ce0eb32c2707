# دليل التثبيت والإعداد - نظام إدارة المدارس

## المتطلبات الأساسية

### 1. متطلبات النظام
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, أو Linux Ubuntu 18.04+
- **الذاكرة**: 4 GB RAM كحد أدنى (8 GB مُوصى به)
- **مساحة القرص**: 500 MB للبرنامج + مساحة إضافية لقاعدة البيانات
- **الشاشة**: دقة 1024x768 كحد أدنى (1920x1080 مُوصى به)

### 2. البرمجيات المطلوبة
- **Python 3.8 أو أحدث** - [تحميل من python.org](https://www.python.org/downloads/)
- **MySQL Server 8.0 أو أحدث** - [تحميل من mysql.com](https://dev.mysql.com/downloads/mysql/)

## خطوات التثبيت

### الخطوة 1: تثبيت Python

#### Windows:
1. حمل Python من الموقع الرسمي
2. شغل ملف التثبيت
3. **مهم**: تأكد من تحديد "Add Python to PATH"
4. اختر "Install Now"

#### macOS:
```bash
# باستخدام Homebrew
brew install python3

# أو حمل من الموقع الرسمي
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install python3 python3-pip python3-tk
```

### الخطوة 2: تثبيت MySQL Server

#### Windows:
1. حمل MySQL Installer من الموقع الرسمي
2. اختر "Developer Default" للتثبيت الكامل
3. اتبع معالج التثبيت
4. احفظ كلمة مرور المستخدم root

#### macOS:
```bash
# باستخدام Homebrew
brew install mysql
brew services start mysql

# تأمين التثبيت
mysql_secure_installation
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql

# تأمين التثبيت
sudo mysql_secure_installation
```

### الخطوة 3: تحميل نظام إدارة المدارس

#### الطريقة 1: تحميل مباشر
1. حمل الملفات من المستودع
2. فك الضغط في مجلد مناسب

#### الطريقة 2: استخدام Git
```bash
git clone [repository-url]
cd school-management-system
```

### الخطوة 4: تثبيت المكتبات المطلوبة

```bash
# انتقل إلى مجلد المشروع
cd school-management-system

# تثبيت المكتبات
pip install -r requirements.txt
```

إذا واجهت مشاكل، جرب:
```bash
pip3 install -r requirements.txt
```

أو تثبيت كل مكتبة على حدة:
```bash
pip install mysql-connector-python==8.2.0
pip install Pillow==10.1.0
pip install tkcalendar==1.6.1
pip install reportlab==4.0.7
pip install openpyxl==3.1.2
```

### الخطوة 5: إعداد قاعدة البيانات

#### الطريقة 1: استخدام معالج الإعداد (مُوصى به)
```bash
python setup_database.py
```

1. أدخل بيانات الاتصال بـ MySQL
2. اضغط "اختبار الاتصال"
3. اضغط "إنشاء قاعدة البيانات"
4. اضغط "إنشاء الجداول"

#### الطريقة 2: إعداد يدوي
1. افتح MySQL Command Line أو MySQL Workbench
2. نفذ الأوامر التالية:

```sql
CREATE DATABASE school_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE school_management;
SOURCE database/school_db.sql;
```

### الخطوة 6: تحديث إعدادات قاعدة البيانات

عدل الملف `config/database_config.py`:

```python
DB_CONFIG = {
    'host': 'localhost',        # عنوان الخادم
    'database': 'school_management',  # اسم قاعدة البيانات
    'user': 'root',            # اسم المستخدم
    'password': 'your_password',  # كلمة المرور
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci',
    'autocommit': True
}
```

## تشغيل النظام

### التشغيل العادي
```bash
python main.py
```

### التشغيل مع فحص المتطلبات
```bash
python run.py
```

### التشغيل السريع مع واجهة مساعدة
```bash
python quick_start.py
```

## بيانات الدخول الافتراضية

**المدير الرئيسي:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## حل المشاكل الشائعة

### مشكلة: "ModuleNotFoundError: No module named 'mysql'"
**الحل:**
```bash
pip install mysql-connector-python
```

### مشكلة: "Access denied for user 'root'@'localhost'"
**الحل:**
1. تأكد من كلمة مرور MySQL
2. أو أنشئ مستخدم جديد:
```sql
CREATE USER 'school_admin'@'localhost' IDENTIFIED BY 'password123';
GRANT ALL PRIVILEGES ON school_management.* TO 'school_admin'@'localhost';
FLUSH PRIVILEGES;
```

### مشكلة: "tkinter not found" في Linux
**الحل:**
```bash
sudo apt-get install python3-tk
```

### مشكلة: خطأ في تشفير الأحرف العربية
**الحل:**
1. تأكد من أن قاعدة البيانات تستخدم utf8mb4
2. تأكد من إعدادات الـ collation

### مشكلة: النافذة لا تظهر بشكل صحيح
**الحل:**
1. تأكد من دقة الشاشة (1024x768 كحد أدنى)
2. تحديث برامج تشغيل كرت الشاشة

## الاختبار

### اختبار سريع
1. شغل البرنامج
2. سجل دخول بحساب المدير
3. جرب إضافة طالب جديد
4. تحقق من ظهور الطالب في القائمة

### اختبار شامل
```bash
# تشغيل اختبارات النظام (إذا كانت متوفرة)
python -m pytest tests/
```

## النسخ الاحتياطي

### إنشاء نسخة احتياطية من قاعدة البيانات
```bash
mysqldump -u root -p school_management > backup_$(date +%Y%m%d).sql
```

### استعادة النسخة الاحتياطية
```bash
mysql -u root -p school_management < backup_20231201.sql
```

## التحديث

### تحديث المكتبات
```bash
pip install --upgrade -r requirements.txt
```

### تحديث النظام
1. احفظ نسخة احتياطية من قاعدة البيانات
2. حمل الإصدار الجديد
3. شغل سكريبت التحديث (إن وُجد)

## الأمان

### تأمين قاعدة البيانات
1. غير كلمة مرور المدير الافتراضية
2. أنشئ مستخدمين منفصلين لكل نوع
3. استخدم كلمات مرور قوية
4. فعل جدار الحماية

### تأمين الملفات
1. احم مجلد المشروع من الوصول غير المصرح
2. اعمل نسخ احتياطية دورية
3. احفظ النسخ الاحتياطية في مكان آمن

## الدعم الفني

### الحصول على المساعدة
1. راجع ملف README.md
2. تحقق من ملف المشاكل الشائعة
3. ابحث في الوثائق
4. اتصل بفريق الدعم

### الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
1. نظام التشغيل والإصدار
2. إصدار Python
3. رسالة الخطأ كاملة
4. خطوات إعادة إنتاج المشكلة

---

**ملاحظة:** هذا الدليل يغطي التثبيت الأساسي. للإعدادات المتقدمة، راجع الوثائق التقنية.
