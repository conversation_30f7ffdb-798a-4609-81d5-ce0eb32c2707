#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لنظام إدارة المدارس
Quick Start for School Management System
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        return False, f"يتطلب Python 3.8 أو أحدث. الإصدار الحالي: {sys.version}"
    return True, "إصدار Python مناسب"

def check_tkinter():
    """التحقق من توفر Tkinter"""
    try:
        import tkinter
        return True, "Tkinter متوفر"
    except ImportError:
        return False, "Tkinter غير متوفر. يرجى تثبيته أولاً"

def check_mysql_connector():
    """التحقق من mysql-connector-python"""
    try:
        import mysql.connector
        return True, "MySQL Connector متوفر"
    except ImportError:
        return False, "MySQL Connector غير متوفر"

def show_requirements_dialog():
    """عرض نافذة المتطلبات"""
    root = tk.Tk()
    root.title("متطلبات النظام")
    root.geometry("500x400")
    root.resizable(False, False)
    
    # العنوان
    title_label = tk.Label(root, text="نظام إدارة المدارس", 
                          font=('Arial', 16, 'bold'), fg='blue')
    title_label.pack(pady=10)
    
    # النص التوضيحي
    info_text = """
مرحباً بك في نظام إدارة المدارس!

للبدء في استخدام النظام، يرجى التأكد من توفر المتطلبات التالية:

1. Python 3.8 أو أحدث
2. MySQL Server
3. المكتبات المطلوبة:
   - mysql-connector-python
   - Pillow
   - tkcalendar
   - reportlab
   - openpyxl

لتثبيت المكتبات المطلوبة، استخدم الأمر:
pip install -r requirements.txt

أو استخدم الملف run.py للتثبيت التلقائي.
    """
    
    info_label = tk.Label(root, text=info_text, justify=tk.LEFT, 
                         font=('Arial', 10), wraplength=450)
    info_label.pack(padx=20, pady=10)
    
    # أزرار
    button_frame = tk.Frame(root)
    button_frame.pack(pady=20)
    
    def try_run():
        root.destroy()
        try:
            from main import main
            main()
        except Exception as e:
            error_root = tk.Tk()
            error_root.withdraw()
            messagebox.showerror("خطأ", f"خطأ في تشغيل البرنامج:\n{str(e)}")
            error_root.destroy()
    
    def install_requirements():
        root.destroy()
        os.system("pip install -r requirements.txt")
        messagebox.showinfo("تثبيت", "تم محاولة تثبيت المتطلبات. يرجى إعادة تشغيل البرنامج.")
    
    tk.Button(button_frame, text="تشغيل البرنامج", command=try_run,
              bg='green', fg='white', font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=5)
    
    tk.Button(button_frame, text="تثبيت المتطلبات", command=install_requirements,
              bg='orange', fg='white', font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=5)
    
    tk.Button(button_frame, text="إغلاق", command=root.destroy,
              bg='red', fg='white', font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=5)
    
    # توسيط النافذة
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    root.mainloop()

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("نظام إدارة المدارس - التشغيل السريع")
    print("School Management System - Quick Start")
    print("=" * 60)
    
    # فحص المتطلبات
    checks = [
        ("إصدار Python", check_python_version),
        ("مكتبة Tkinter", check_tkinter),
        ("MySQL Connector", check_mysql_connector)
    ]
    
    all_ok = True
    
    for name, check_func in checks:
        try:
            ok, message = check_func()
            status = "✓" if ok else "✗"
            print(f"{status} {name}: {message}")
            if not ok:
                all_ok = False
        except Exception as e:
            print(f"✗ {name}: خطأ في الفحص - {str(e)}")
            all_ok = False
    
    print("=" * 60)
    
    if all_ok:
        print("جميع المتطلبات متوفرة! تشغيل البرنامج...")
        try:
            from main import main as run_main
            run_main()
        except Exception as e:
            print(f"خطأ في تشغيل البرنامج: {e}")
            show_requirements_dialog()
    else:
        print("بعض المتطلبات غير متوفرة. عرض نافذة المساعدة...")
        show_requirements_dialog()

if __name__ == "__main__":
    main()
