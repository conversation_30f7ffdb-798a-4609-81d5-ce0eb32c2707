# دليل المستخدم - نظام إدارة المدارس

## مقدمة

مرحباً بك في نظام إدارة المدارس! هذا الدليل سيساعدك على فهم واستخدام جميع ميزات النظام بكفاءة.

## البدء السريع

### تسجيل الدخول
1. شغل البرنامج من خلال النقر على `main.py` أو `run.py`
2. ستظهر نافذة تسجيل الدخول
3. أدخل بيانات الدخول:
   - **المدير الافتراضي**: اسم المستخدم `admin` وكلمة المرور `admin123`
4. اضغط "تسجيل الدخول"

### الواجهة الرئيسية
بعد تسجيل الدخول بنجاح، ستظهر النافذة الرئيسية التي تحتوي على:
- **شريط القوائم العلوي**: يحتوي على جميع الوظائف الرئيسية
- **الشريط الجانبي**: أزرار سريعة للوظائف الأكثر استخداماً
- **منطقة المحتوى**: تعرض المعلومات والنماذج
- **شريط الحالة**: يعرض معلومات المستخدم والوقت

## إدارة الطلاب

### إضافة طالب جديد
1. من القائمة الرئيسية، اختر **"الطلاب"** ← **"إدارة الطلاب"**
2. أو اضغط على زر **"👥 الطلاب"** من الشريط الجانبي
3. اضغط **"إضافة طالب جديد"**
4. املأ النموذج:
   - **رقم الطالب**: رقم فريد للطالب
   - **الاسم الكامل**: الاسم الثلاثي أو الرباعي
   - **تاريخ الميلاد**: استخدم منتقي التاريخ
   - **الجنس**: ذكر أو أنثى
   - **معلومات الاتصال**: البريد الإلكتروني والهاتف والعنوان
   - **الصف**: اختر من القائمة المنسدلة
   - **ولي الأمر**: اختر من قائمة أولياء الأمور المسجلين
   - **المعلومات الطبية**: فصيلة الدم والملاحظات الطبية
5. اضغط **"حفظ"**

### البحث عن طالب
1. في نافذة إدارة الطلاب، استخدم حقل البحث
2. يمكنك البحث بـ:
   - رقم الطالب
   - الاسم (جزئي أو كامل)
3. ستظهر النتائج فوراً في القائمة

### تعديل بيانات طالب
1. ابحث عن الطالب أو اختره من القائمة
2. انقر نقراً مزدوجاً على اسم الطالب
3. ستظهر بياناته في النموذج
4. عدل البيانات المطلوبة
5. اضغط **"تعديل"**

### حذف طالب
1. اختر الطالب من القائمة
2. اضغط **"حذف"** أو انقر بالزر الأيمن واختر "حذف"
3. أكد عملية الحذف
4. **تحذير**: هذا الإجراء لا يمكن التراجع عنه

## إدارة المعلمين

### إضافة معلم جديد
1. اختر **"المعلمون"** من القائمة الرئيسية
2. اضغط **"إضافة معلم جديد"**
3. املأ البيانات:
   - **رقم المعلم**: رقم فريد
   - **الاسم الكامل**
   - **التخصص**: مثل "رياضيات" أو "لغة عربية"
   - **المؤهل العلمي**: مثل "بكالوريوس" أو "ماجستير"
   - **سنوات الخبرة**
   - **تاريخ التوظيف**
   - **الراتب** (اختياري)
   - **معلومات الاتصال**
4. اضغط **"حفظ"**

### تعيين معلم لصف
1. اذهب إلى **"الصفوف والجداول"** ← **"إدارة الصفوف"**
2. اختر الصف المطلوب
3. من قائمة "معلم الصف"، اختر المعلم المناسب
4. احفظ التغييرات

## إدارة الصفوف

### إنشاء صف جديد
1. اختر **"الصفوف والجداول"** ← **"إدارة الصفوف"**
2. اضغط **"إضافة صف جديد"**
3. أدخل:
   - **اسم الصف**: مثل "الأول الابتدائي"
   - **المستوى**: رقم المستوى (1، 2، 3...)
   - **القسم**: مثل "أ" أو "ب"
   - **السعة**: العدد الأقصى للطلاب
   - **معلم الصف**: اختياري
   - **السنة الأكاديمية**: تُملأ تلقائياً
4. احفظ البيانات

### عرض طلاب الصف
1. في قائمة الصفوف، اختر الصف المطلوب
2. انقر **"عرض الطلاب"**
3. ستظهر قائمة بجميع الطلاب المسجلين في هذا الصف

## الحضور والغياب

### تسجيل الحضور اليومي
1. اختر **"الحضور والغياب"** ← **"تسجيل الحضور"**
2. اختر الصف والتاريخ
3. لكل طالب، اختر:
   - **حاضر**: الطالب موجود
   - **غائب**: الطالب غير موجود
   - **متأخر**: الطالب وصل متأخراً
   - **غياب بعذر**: غياب مبرر
4. أضف ملاحظات إذا لزم الأمر
5. احفظ بيانات الحضور

### عرض تقارير الحضور
1. اختر **"الحضور والغياب"** ← **"تقارير الحضور"**
2. حدد:
   - الصف أو الطالب
   - الفترة الزمنية (من تاريخ إلى تاريخ)
3. اضغط **"إنشاء التقرير"**
4. يمكنك طباعة أو تصدير التقرير

## الدرجات والامتحانات

### إنشاء امتحان جديد
1. اختر **"الدرجات والامتحانات"** ← **"إدارة الامتحانات"**
2. اضغط **"إضافة امتحان جديد"**
3. أدخل:
   - **اسم الامتحان**: مثل "امتحان الشهر الأول"
   - **المادة**: اختر من القائمة
   - **الصف**: اختر الصف المستهدف
   - **تاريخ الامتحان**
   - **وقت الامتحان**
   - **المدة**: بالدقائق
   - **الدرجة الكاملة**: مثل 100
   - **نوع الامتحان**: اختبار، امتحان نصفي، امتحان نهائي، واجب
4. احفظ بيانات الامتحان

### إدخال الدرجات
1. اختر **"الدرجات والامتحانات"** ← **"إدخال الدرجات"**
2. اختر الامتحان من القائمة
3. ستظهر قائمة بجميع الطلاب المسجلين
4. لكل طالب، أدخل:
   - **الدرجة المحصلة**
   - **التقدير** (يُحسب تلقائياً)
   - **ملاحظات** (اختياري)
5. احفظ الدرجات

### عرض تقارير الدرجات
1. اختر **"الدرجات والامتحانات"** ← **"تقارير الدرجات"**
2. اختر نوع التقرير:
   - تقرير طالب واحد
   - تقرير صف كامل
   - تقرير مادة معينة
3. حدد المعايير المطلوبة
4. أنشئ التقرير

## المالية

### إصدار فاتورة
1. اختر **"المالية"** ← **"الفواتير"**
2. اضغط **"إصدار فاتورة جديدة"**
3. أدخل:
   - **الطالب**: اختر من القائمة
   - **المبلغ**: قيمة الفاتورة
   - **تاريخ الاستحقاق**
   - **الوصف**: سبب الفاتورة (رسوم دراسية، كتب، إلخ)
   - **السنة الأكاديمية**
4. احفظ الفاتورة

### تسجيل دفعة
1. في قائمة الفواتير، اختر الفاتورة المطلوبة
2. اضغط **"تسجيل دفعة"**
3. أدخل:
   - **المبلغ المدفوع**
   - **تاريخ الدفع**
   - **طريقة الدفع**: نقد، تحويل بنكي، شيك، بطاقة
   - **رقم المرجع**: للتحويلات والشيكات
   - **ملاحظات**
4. احفظ بيانات الدفعة

## التقارير

### إنشاء تقرير شامل
1. اختر **"التقارير"** ← **"تقرير شامل"**
2. حدد المعايير:
   - **الفترة الزمنية**
   - **الصفوف المطلوبة**
   - **نوع البيانات**: طلاب، حضور، درجات، مالية
3. اختر تنسيق التقرير: PDF، Excel، CSV
4. اضغط **"إنشاء التقرير"**

### عرض الإحصائيات
1. اختر **"التقارير"** ← **"إحصائيات"**
2. ستظهر لوحة تحكم تحتوي على:
   - إجمالي الطلاب والمعلمين
   - معدلات الحضور
   - الأداء الأكاديمي
   - الحالة المالية
   - رسوم بيانية ومخططات

## الإعدادات

### تغيير كلمة المرور
1. اختر **"ملف"** ← **"إعدادات"**
2. اضغط **"تغيير كلمة المرور"**
3. أدخل كلمة المرور الحالية
4. أدخل كلمة المرور الجديدة مرتين
5. احفظ التغييرات

### تغيير اللغة
1. في نافذة تسجيل الدخول، اضغط **"العربية | Français"**
2. أو من الإعدادات، اختر اللغة المفضلة
3. ستتغير جميع النصوص في الواجهة

### إعدادات قاعدة البيانات
1. اختر **"ملف"** ← **"إعدادات"** ← **"قاعدة البيانات"**
2. يمكنك تعديل:
   - عنوان الخادم
   - اسم قاعدة البيانات
   - بيانات الاتصال
3. **تحذير**: تأكد من صحة البيانات قبل الحفظ

## نصائح مهمة

### الأمان
- غير كلمة مرور المدير الافتراضية فوراً
- لا تشارك بيانات الدخول مع أشخاص غير مخولين
- اعمل نسخة احتياطية من البيانات بانتظام

### الأداء
- أغلق النوافذ غير المستخدمة لتحسين الأداء
- تجنب فتح عدة نسخ من البرنامج في نفس الوقت
- نظف قاعدة البيانات من البيانات القديمة دورياً

### النسخ الاحتياطي
- اعمل نسخة احتياطية يومية من قاعدة البيانات
- احفظ النسخ الاحتياطية في مكان آمن
- اختبر استعادة النسخ الاحتياطية دورياً

## الدعم الفني

إذا واجهت أي مشكلة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف المشاكل الشائعة في INSTALLATION_GUIDE.md
3. تأكد من أن جميع المتطلبات مثبتة بشكل صحيح
4. اتصل بفريق الدعم الفني

---

**ملاحظة**: هذا الدليل يغطي الوظائف الأساسية. المزيد من الميزات ستتم إضافتها في الإصدارات القادمة.
