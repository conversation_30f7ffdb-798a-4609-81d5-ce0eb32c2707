#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة المدارس
School Management System Launcher
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_requirements():
    """التحقق من المتطلبات المطلوبة"""
    required_modules = [
        'mysql.connector',
        'PIL',
        'tkcalendar',
        'reportlab',
        'openpyxl'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        return False, missing_modules
    
    return True, []

def install_requirements():
    """تثبيت المتطلبات المطلوبة"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        return True
    except subprocess.CalledProcessError:
        return False

def show_error_dialog(title, message):
    """عرض رسالة خطأ"""
    root = tk.Tk()
    root.withdraw()
    messagebox.showerror(title, message)
    root.destroy()

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    print("=" * 50)
    print("نظام إدارة المدارس")
    print("School Management System")
    print("=" * 50)
    
    # التحقق من المتطلبات
    print("التحقق من المتطلبات...")
    requirements_ok, missing = check_requirements()
    
    if not requirements_ok:
        print(f"المكتبات المفقودة: {', '.join(missing)}")
        print("محاولة تثبيت المتطلبات...")
        
        if install_requirements():
            print("تم تثبيت المتطلبات بنجاح!")
        else:
            error_msg = """
خطأ في تثبيت المتطلبات!

يرجى تثبيت المتطلبات يدوياً باستخدام الأمر:
pip install -r requirements.txt

أو تثبيت كل مكتبة على حدة:
pip install mysql-connector-python
pip install Pillow
pip install tkcalendar
pip install reportlab
pip install openpyxl
            """
            print(error_msg)
            show_error_dialog("خطأ في المتطلبات", error_msg)
            return
    
    # التحقق من وجود الملفات المطلوبة
    required_files = [
        'main.py',
        'config/database_config.py',
        'config/app_config.py',
        'database/school_db.sql'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        error_msg = f"الملفات المفقودة: {', '.join(missing_files)}"
        print(error_msg)
        show_error_dialog("ملفات مفقودة", error_msg)
        return
    
    # تشغيل البرنامج الرئيسي
    print("تشغيل نظام إدارة المدارس...")
    try:
        from main import main as run_main
        run_main()
    except Exception as e:
        error_msg = f"خطأ في تشغيل البرنامج: {str(e)}"
        print(error_msg)
        show_error_dialog("خطأ في التشغيل", error_msg)

if __name__ == "__main__":
    main()
