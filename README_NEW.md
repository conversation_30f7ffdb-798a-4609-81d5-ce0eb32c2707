# نظام إدارة المدارس - School Management System

<div align="center">

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)
![Tkinter](https://img.shields.io/badge/GUI-Tkinter-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)
![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)

**نظام شامل ومتطور لإدارة المدارس باستخدام Python و Tkinter و MySQL**

[التثبيت](#التثبيت) • [الميزات](#الميزات) • [الاستخدام](#الاستخدام) • [التوثيق](#التوثيق) • [الدعم](#الدعم)

</div>

---

## 📋 نظرة عامة

نظام إدارة المدارس هو حل متكامل وشامل مصمم خصيصاً للمؤسسات التعليمية في الوطن العربي. يوفر النظام جميع الأدوات اللازمة لإدارة العمليات اليومية للمدرسة بكفاءة وسهولة.

### 🎯 الهدف من النظام
- تبسيط العمليات الإدارية والأكاديمية
- توفير واجهة سهلة الاستخدام باللغة العربية
- ضمان أمان وسرية البيانات
- توفير تقارير شاملة ومفصلة
- تحسين التواصل بين المدرسة وأولياء الأمور

## ✨ الميزات الرئيسية

### 👥 إدارة الطلاب والمعلمين
- **إدارة شاملة للطلاب**: تسجيل، تعديل، حذف، وبحث متقدم
- **إدارة المعلمين**: تسجيل المعلمين مع تخصصاتهم ومؤهلاتهم
- **إدارة أولياء الأمور**: ربط الطلاب بأولياء أمورهم
- **نظام الصفوف**: تنظيم الطلاب في صفوف ومستويات مختلفة

### 📅 الجداول الدراسية والحضور
- **إعداد الجداول**: إنشاء وإدارة الجداول الدراسية
- **تسجيل الحضور**: نظام يومي لتسجيل حضور وغياب الطلاب
- **تقارير الحضور**: تقارير مفصلة عن معدلات الحضور
- **إشعارات الغياب**: تنبيهات تلقائية لأولياء الأمور

### 📊 الدرجات والامتحانات
- **إدارة الامتحانات**: جدولة وتنظيم الامتحانات
- **إدخال الدرجات**: واجهة سهلة لإدخال وتعديل الدرجات
- **حساب المعدلات**: حساب تلقائي للمعدلات والتقديرات
- **تقارير الأداء**: تقارير شاملة عن أداء الطلاب

### 💰 النظام المالي
- **إصدار الفواتير**: إنشاء فواتير الرسوم الدراسية
- **متابعة المدفوعات**: تتبع المدفوعات والمتأخرات
- **التقارير المالية**: تقارير مالية شاملة
- **إدارة الأقساط**: نظام مرن لإدارة الأقساط

### 🔐 الأمان والصلاحيات
- **نظام مستخدمين متعدد**: أدوار مختلفة (مدير، معلم، طالب، ولي أمر)
- **تشفير كلمات المرور**: حماية متقدمة للبيانات
- **صلاحيات محددة**: تحكم دقيق في صلاحيات كل مستخدم
- **سجل العمليات**: تتبع جميع العمليات المنجزة

### 🌐 الدعم متعدد اللغات
- **اللغة العربية**: واجهة كاملة باللغة العربية
- **اللغة الفرنسية**: دعم كامل للغة الفرنسية
- **تبديل سهل**: إمكانية تغيير اللغة بنقرة واحدة

### 📈 التقارير والإحصائيات
- **تقارير شاملة**: تقارير مفصلة عن جميع جوانب المدرسة
- **إحصائيات مرئية**: رسوم بيانية ومخططات توضيحية
- **تصدير البيانات**: تصدير التقارير بصيغ مختلفة (PDF, Excel, CSV)
- **طباعة مباشرة**: إمكانية طباعة التقارير مباشرة

## 🛠 المتطلبات التقنية

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, Linux Ubuntu 18.04+
- **المعالج**: Intel Core i3 أو ما يعادله
- **الذاكرة**: 4 GB RAM (8 GB مُوصى به)
- **مساحة القرص**: 500 MB للبرنامج + مساحة إضافية للبيانات
- **الشاشة**: دقة 1024x768 كحد أدنى (1920x1080 مُوصى به)

### البرمجيات المطلوبة
- **Python 3.8+**: [تحميل من python.org](https://www.python.org/downloads/)
- **MySQL Server 8.0+**: [تحميل من mysql.com](https://dev.mysql.com/downloads/mysql/)

### المكتبات المطلوبة
```
mysql-connector-python==8.2.0
Pillow==10.1.0
tkcalendar==1.6.1
reportlab==4.0.7
openpyxl==3.1.2
```

## 🚀 التثبيت السريع

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd school-management-system
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات
```bash
python setup_database.py
```

### 4. تشغيل النظام
```bash
python main.py
```

## 📖 التوثيق

- **[دليل التثبيت المفصل](INSTALLATION_GUIDE.md)**: خطوات التثبيت التفصيلية
- **[دليل المستخدم](USER_GUIDE.md)**: شرح شامل لجميع الميزات
- **[الوثائق التقنية](docs/)**: معلومات تقنية للمطورين

## 🎮 الاستخدام

### بيانات الدخول الافتراضية
- **المدير الرئيسي**: 
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`

### التشغيل السريع
```bash
# للتشغيل مع فحص المتطلبات
python run.py

# للتشغيل مع واجهة المساعدة
python quick_start.py

# لاختبار النظام
python test_system.py
```

## 🏗 هيكل المشروع

```
school-management-system/
├── 📁 config/              # ملفات الإعدادات
│   ├── app_config.py       # إعدادات التطبيق
│   └── database_config.py  # إعدادات قاعدة البيانات
├── 📁 database/            # قاعدة البيانات
│   └── school_db.sql       # هيكل قاعدة البيانات
├── 📁 gui/                 # واجهات المستخدم
│   ├── login_window.py     # نافذة تسجيل الدخول
│   ├── main_window.py      # النافذة الرئيسية
│   └── students_window.py  # نافذة إدارة الطلاب
├── 📁 models/              # نماذج البيانات
│   ├── student.py          # نموذج الطالب
│   ├── teacher.py          # نموذج المعلم
│   └── class_model.py      # نموذج الصف
├── 📁 utils/               # أدوات مساعدة
│   └── security.py         # أدوات الأمان
├── 📁 docs/                # التوثيق
├── main.py                 # الملف الرئيسي
├── run.py                  # ملف التشغيل مع الفحص
├── setup_database.py       # معالج إعداد قاعدة البيانات
├── test_system.py          # اختبارات النظام
└── requirements.txt        # المتطلبات
```

## 🔧 الإعداد المتقدم

### إعداد قاعدة البيانات يدوياً
```sql
CREATE DATABASE school_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE school_management;
SOURCE database/school_db.sql;
```

### تخصيص الإعدادات
عدل الملف `config/database_config.py` لتخصيص إعدادات قاعدة البيانات:
```python
DB_CONFIG = {
    'host': 'localhost',
    'database': 'school_management',
    'user': 'your_username',
    'password': 'your_password',
    'charset': 'utf8mb4'
}
```

## 🧪 الاختبار

### اختبار سريع
```bash
python test_system.py
```

### اختبار شامل
```bash
python test_system.py --unittest
```

## 📊 لقطات الشاشة

### نافذة تسجيل الدخول
- واجهة أنيقة وبسيطة
- دعم متعدد اللغات
- نظام أمان متقدم

### النافذة الرئيسية
- لوحة تحكم شاملة
- إحصائيات مرئية
- قوائم منظمة

### إدارة الطلاب
- نماذج مفصلة
- بحث متقدم
- تصدير البيانات

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:
1. عمل Fork للمستودع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🆘 الدعم والمساعدة

### الحصول على المساعدة
- راجع [دليل المستخدم](USER_GUIDE.md)
- تحقق من [دليل التثبيت](INSTALLATION_GUIDE.md)
- ابحث في [المشاكل الشائعة](#المشاكل-الشائعة)

### الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
- نظام التشغيل والإصدار
- إصدار Python
- رسالة الخطأ كاملة
- خطوات إعادة إنتاج المشكلة

## 🔄 التحديثات

### الإصدار الحالي: v1.0.0
- إطلاق النسخة الأولى
- جميع الميزات الأساسية متوفرة
- دعم كامل للغة العربية

### التحديثات القادمة
- تطبيق الهاتف المحمول
- واجهة ويب
- تكامل مع أنظمة خارجية
- المزيد من التقارير

---

<div align="center">

**تم تطوير هذا النظام بعناية فائقة لخدمة المؤسسات التعليمية العربية**

⭐ إذا أعجبك المشروع، لا تنس إعطاؤه نجمة!

</div>
