# واجهة إدارة الطلاب
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from tkcalendar import DateEntry

from config.app_config import AppConfig
from models.student import Student
from config.database_config import Database

class StudentsWindow:
    """واجهة إدارة الطلاب"""
    
    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.student_model = Student()
        self.db = Database()
        
        # متغيرات النموذج
        self.setup_variables()
        
        # بيانات الطلاب
        self.students_data = []
        self.filtered_data = []
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.student_id_var = tk.StringVar()
        self.full_name_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.phone_var = tk.StringVar()
        self.address_var = tk.StringVar()
        self.gender_var = tk.StringVar(value='male')
        self.blood_type_var = tk.StringVar()
        self.medical_notes_var = tk.StringVar()
        self.class_var = tk.StringVar()
        self.parent_var = tk.StringVar()
        self.search_var = tk.StringVar()
        
        # متغير تاريخ الميلاد
        self.birth_date = None
        self.enrollment_date = date.today()
    
    def show(self):
        """عرض نافذة إدارة الطلاب"""
        self.window = tk.Toplevel(self.parent)
        self.setup_window()
        self.create_widgets()
        self.load_students()
        self.load_classes()
        self.load_parents()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الطلاب")
        self.window.geometry("1000x700")
        self.window.configure(bg=AppConfig.COLORS['background'])
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="إدارة الطلاب", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # إطار البحث والأزرار
        self.create_search_frame(main_frame)
        
        # إطار المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # قائمة الطلاب
        self.create_students_list(content_frame)
        
        # نموذج إضافة/تعديل الطالب
        self.create_student_form(content_frame)
    
    def create_search_frame(self, parent):
        """إنشاء إطار البحث والأزرار"""
        search_frame = ttk.Frame(parent)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # البحث
        ttk.Label(search_frame, text="البحث:").pack(side=tk.LEFT, padx=(0, 5))
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # أزرار العمليات
        ttk.Button(search_frame, text="إضافة طالب جديد", 
                  command=self.add_new_student).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(search_frame, text="تحديث القائمة", 
                  command=self.refresh_list).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(search_frame, text="تصدير إلى Excel", 
                  command=self.export_to_excel).pack(side=tk.RIGHT, padx=(5, 0))
    
    def create_students_list(self, parent):
        """إنشاء قائمة الطلاب"""
        # إطار القائمة
        list_frame = ttk.LabelFrame(parent, text="قائمة الطلاب")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # إنشاء Treeview
        columns = ('student_id', 'full_name', 'class', 'gender', 'phone', 'status')
        self.students_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # تعريف العناوين
        self.students_tree.heading('student_id', text='رقم الطالب')
        self.students_tree.heading('full_name', text='الاسم الكامل')
        self.students_tree.heading('class', text='الصف')
        self.students_tree.heading('gender', text='الجنس')
        self.students_tree.heading('phone', text='الهاتف')
        self.students_tree.heading('status', text='الحالة')
        
        # تعريف عرض الأعمدة
        self.students_tree.column('student_id', width=100)
        self.students_tree.column('full_name', width=200)
        self.students_tree.column('class', width=100)
        self.students_tree.column('gender', width=80)
        self.students_tree.column('phone', width=120)
        self.students_tree.column('status', width=80)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.students_tree.yview)
        self.students_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط العناصر
        self.students_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.students_tree.bind('<Double-1>', self.on_student_select)
        self.students_tree.bind('<Button-3>', self.show_context_menu)
    
    def create_student_form(self, parent):
        """إنشاء نموذج إضافة/تعديل الطالب"""
        # إطار النموذج
        form_frame = ttk.LabelFrame(parent, text="بيانات الطالب")
        form_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        
        # إطار التمرير
        canvas = tk.Canvas(form_frame, width=350)
        scrollbar = ttk.Scrollbar(form_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # المعلومات الأساسية
        basic_frame = ttk.LabelFrame(scrollable_frame, text="المعلومات الأساسية")
        basic_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # رقم الطالب
        ttk.Label(basic_frame, text="رقم الطالب:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(basic_frame, textvariable=self.student_id_var, width=25).grid(row=0, column=1, padx=5, pady=2)
        
        # الاسم الكامل
        ttk.Label(basic_frame, text="الاسم الكامل:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(basic_frame, textvariable=self.full_name_var, width=25).grid(row=1, column=1, padx=5, pady=2)
        
        # تاريخ الميلاد
        ttk.Label(basic_frame, text="تاريخ الميلاد:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.birth_date_entry = DateEntry(basic_frame, width=22, background='darkblue',
                                         foreground='white', borderwidth=2, date_pattern='yyyy-mm-dd')
        self.birth_date_entry.grid(row=2, column=1, padx=5, pady=2)
        
        # الجنس
        ttk.Label(basic_frame, text="الجنس:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        gender_frame = ttk.Frame(basic_frame)
        gender_frame.grid(row=3, column=1, padx=5, pady=2, sticky=tk.W)
        ttk.Radiobutton(gender_frame, text="ذكر", variable=self.gender_var, value='male').pack(side=tk.LEFT)
        ttk.Radiobutton(gender_frame, text="أنثى", variable=self.gender_var, value='female').pack(side=tk.LEFT, padx=(10, 0))
        
        # معلومات الاتصال
        contact_frame = ttk.LabelFrame(scrollable_frame, text="معلومات الاتصال")
        contact_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # البريد الإلكتروني
        ttk.Label(contact_frame, text="البريد الإلكتروني:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(contact_frame, textvariable=self.email_var, width=25).grid(row=0, column=1, padx=5, pady=2)
        
        # الهاتف
        ttk.Label(contact_frame, text="الهاتف:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(contact_frame, textvariable=self.phone_var, width=25).grid(row=1, column=1, padx=5, pady=2)
        
        # العنوان
        ttk.Label(contact_frame, text="العنوان:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        address_text = tk.Text(contact_frame, width=25, height=3)
        address_text.grid(row=2, column=1, padx=5, pady=2)
        self.address_text = address_text
        
        # المعلومات الأكاديمية
        academic_frame = ttk.LabelFrame(scrollable_frame, text="المعلومات الأكاديمية")
        academic_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # الصف
        ttk.Label(academic_frame, text="الصف:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.class_combo = ttk.Combobox(academic_frame, textvariable=self.class_var, width=22, state='readonly')
        self.class_combo.grid(row=0, column=1, padx=5, pady=2)
        
        # ولي الأمر
        ttk.Label(academic_frame, text="ولي الأمر:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.parent_combo = ttk.Combobox(academic_frame, textvariable=self.parent_var, width=22, state='readonly')
        self.parent_combo.grid(row=1, column=1, padx=5, pady=2)
        
        # المعلومات الطبية
        medical_frame = ttk.LabelFrame(scrollable_frame, text="المعلومات الطبية")
        medical_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # فصيلة الدم
        ttk.Label(medical_frame, text="فصيلة الدم:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        blood_combo = ttk.Combobox(medical_frame, textvariable=self.blood_type_var, width=22)
        blood_combo['values'] = ('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-')
        blood_combo.grid(row=0, column=1, padx=5, pady=2)
        
        # ملاحظات طبية
        ttk.Label(medical_frame, text="ملاحظات طبية:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        medical_text = tk.Text(medical_frame, width=25, height=3)
        medical_text.grid(row=1, column=1, padx=5, pady=2)
        self.medical_text = medical_text
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(scrollable_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="حفظ", command=self.save_student).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="تعديل", command=self.edit_student).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="حذف", command=self.delete_student).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="جديد", command=self.clear_form).pack(side=tk.LEFT, padx=5)
        
        # تخطيط Canvas والـ Scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            self.students_data = self.student_model.get_all_students()
            self.filtered_data = self.students_data.copy()
            self.update_students_tree()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل قائمة الطلاب: {str(e)}")
    
    def update_students_tree(self):
        """تحديث عرض قائمة الطلاب"""
        # مسح البيانات الحالية
        for item in self.students_tree.get_children():
            self.students_tree.delete(item)
        
        # إضافة البيانات الجديدة
        for student in self.filtered_data:
            gender_text = "ذكر" if student['gender'] == 'male' else "أنثى"
            status_text = "نشط" if student['status'] == 'active' else "غير نشط"
            class_text = f"{student['class_name']} - {student['section']}" if student['class_name'] else "غير محدد"
            
            self.students_tree.insert('', tk.END, values=(
                student['student_id'],
                student['full_name'],
                class_text,
                gender_text,
                student['phone'] or '',
                status_text
            ))
    
    def load_classes(self):
        """تحميل قائمة الصفوف"""
        try:
            if not self.db.connect():
                return
            
            query = "SELECT id, class_name, grade_level, section FROM classes ORDER BY grade_level, class_name"
            classes = self.db.execute_query(query)
            
            if classes:
                class_values = [f"{cls['class_name']} - {cls['section']} (المستوى {cls['grade_level']})" for cls in classes]
                self.class_combo['values'] = class_values
                self.classes_data = classes
            
        except Exception as e:
            print(f"خطأ في تحميل الصفوف: {e}")
        finally:
            self.db.disconnect()
    
    def load_parents(self):
        """تحميل قائمة أولياء الأمور"""
        try:
            if not self.db.connect():
                return
            
            query = "SELECT id, full_name FROM users WHERE user_type = 'parent' ORDER BY full_name"
            parents = self.db.execute_query(query)
            
            if parents:
                parent_values = [f"{parent['full_name']}" for parent in parents]
                self.parent_combo['values'] = parent_values
                self.parents_data = parents
            
        except Exception as e:
            print(f"خطأ في تحميل أولياء الأمور: {e}")
        finally:
            self.db.disconnect()
    
    def on_search(self, event=None):
        """البحث في الطلاب"""
        search_term = self.search_var.get().strip()
        
        if not search_term:
            self.filtered_data = self.students_data.copy()
        else:
            self.filtered_data = [
                student for student in self.students_data
                if search_term.lower() in student['full_name'].lower() or
                   search_term in student['student_id']
            ]
        
        self.update_students_tree()
    
    def on_student_select(self, event):
        """معالج اختيار طالب من القائمة"""
        selection = self.students_tree.selection()
        if selection:
            item = self.students_tree.item(selection[0])
            student_id = item['values'][0]
            self.load_student_data(student_id)
    
    def load_student_data(self, student_id):
        """تحميل بيانات طالب في النموذج"""
        student = self.student_model.get_student_by_id(student_id)
        if student:
            self.student_id_var.set(student['student_id'])
            self.full_name_var.set(student['full_name'])
            self.email_var.set(student['email'] or '')
            self.phone_var.set(student['phone'] or '')
            self.address_text.delete(1.0, tk.END)
            self.address_text.insert(1.0, student['address'] or '')
            self.gender_var.set(student['gender'])
            self.blood_type_var.set(student['blood_type'] or '')
            self.medical_text.delete(1.0, tk.END)
            self.medical_text.insert(1.0, student['medical_notes'] or '')
            
            # تاريخ الميلاد
            if student['date_of_birth']:
                self.birth_date_entry.set_date(student['date_of_birth'])
    
    def save_student(self):
        """حفظ بيانات الطالب"""
        if not self.validate_form():
            return
        
        student_data = self.get_form_data()
        
        # التحقق من وجود الطالب
        if self.student_model.student_id_exists(student_data['student_id']):
            messagebox.showerror("خطأ", "رقم الطالب موجود مسبقاً")
            return
        
        success, message = self.student_model.add_student(student_data)
        
        if success:
            messagebox.showinfo("نجح", message)
            self.clear_form()
            self.load_students()
        else:
            messagebox.showerror("خطأ", message)
    
    def edit_student(self):
        """تعديل بيانات الطالب"""
        student_id = self.student_id_var.get().strip()
        if not student_id:
            messagebox.showerror("خطأ", "يرجى اختيار طالب للتعديل")
            return
        
        if not self.validate_form():
            return
        
        student_data = self.get_form_data()
        success, message = self.student_model.update_student(student_id, student_data)
        
        if success:
            messagebox.showinfo("نجح", message)
            self.load_students()
        else:
            messagebox.showerror("خطأ", message)
    
    def delete_student(self):
        """حذف طالب"""
        student_id = self.student_id_var.get().strip()
        if not student_id:
            messagebox.showerror("خطأ", "يرجى اختيار طالب للحذف")
            return
        
        result = messagebox.askyesno("تأكيد الحذف", 
                                   f"هل تريد حذف الطالب {self.full_name_var.get()}؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه.")
        
        if result:
            success, message = self.student_model.delete_student(student_id)
            
            if success:
                messagebox.showinfo("نجح", message)
                self.clear_form()
                self.load_students()
            else:
                messagebox.showerror("خطأ", message)
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.student_id_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم الطالب")
            return False
        
        if not self.full_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال الاسم الكامل")
            return False
        
        return True
    
    def get_form_data(self):
        """الحصول على بيانات النموذج"""
        # الحصول على معرف الصف
        class_id = None
        class_selection = self.class_var.get()
        if class_selection and hasattr(self, 'classes_data'):
            for cls in self.classes_data:
                if f"{cls['class_name']} - {cls['section']} (المستوى {cls['grade_level']})" == class_selection:
                    class_id = cls['id']
                    break
        
        # الحصول على معرف ولي الأمر
        parent_id = None
        parent_selection = self.parent_var.get()
        if parent_selection and hasattr(self, 'parents_data'):
            for parent in self.parents_data:
                if parent['full_name'] == parent_selection:
                    parent_id = parent['id']
                    break
        
        return {
            'student_id': self.student_id_var.get().strip(),
            'full_name': self.full_name_var.get().strip(),
            'email': self.email_var.get().strip(),
            'phone': self.phone_var.get().strip(),
            'address': self.address_text.get(1.0, tk.END).strip(),
            'gender': self.gender_var.get(),
            'blood_type': self.blood_type_var.get().strip(),
            'medical_notes': self.medical_text.get(1.0, tk.END).strip(),
            'date_of_birth': self.birth_date_entry.get_date(),
            'enrollment_date': self.enrollment_date,
            'class_id': class_id,
            'parent_id': parent_id
        }
    
    def clear_form(self):
        """مسح النموذج"""
        self.student_id_var.set('')
        self.full_name_var.set('')
        self.email_var.set('')
        self.phone_var.set('')
        self.address_text.delete(1.0, tk.END)
        self.gender_var.set('male')
        self.blood_type_var.set('')
        self.medical_text.delete(1.0, tk.END)
        self.class_var.set('')
        self.parent_var.set('')
        self.birth_date_entry.set_date(date.today())
    
    def add_new_student(self):
        """إضافة طالب جديد"""
        self.clear_form()
    
    def refresh_list(self):
        """تحديث القائمة"""
        self.load_students()
    
    def export_to_excel(self):
        """تصدير إلى Excel"""
        messagebox.showinfo("تصدير", "ميزة التصدير قيد التطوير")
    
    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        # إنشاء قائمة سياقية
        context_menu = tk.Menu(self.window, tearoff=0)
        context_menu.add_command(label="عرض التفاصيل", command=self.show_student_details)
        context_menu.add_command(label="تعديل", command=self.edit_student)
        context_menu.add_command(label="حذف", command=self.delete_student)
        context_menu.add_separator()
        context_menu.add_command(label="عرض الدرجات", command=self.show_student_grades)
        context_menu.add_command(label="عرض الحضور", command=self.show_student_attendance)
        
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
    
    def show_student_details(self):
        """عرض تفاصيل الطالب"""
        selection = self.students_tree.selection()
        if selection:
            item = self.students_tree.item(selection[0])
            student_id = item['values'][0]
            messagebox.showinfo("تفاصيل الطالب", f"عرض تفاصيل الطالب {student_id} قيد التطوير")
    
    def show_student_grades(self):
        """عرض درجات الطالب"""
        messagebox.showinfo("درجات الطالب", "عرض درجات الطالب قيد التطوير")
    
    def show_student_attendance(self):
        """عرض حضور الطالب"""
        messagebox.showinfo("حضور الطالب", "عرض حضور الطالب قيد التطوير")
