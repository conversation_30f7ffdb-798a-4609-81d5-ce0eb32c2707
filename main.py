#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المدارس - الملف الرئيسي
School Management System - Main File
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.app_config import AppConfig, LanguageConfig
from config.database_config import DatabaseConfig
from gui.login_window import LoginWindow
from gui.main_window import MainWindow
from utils.security import session_manager

class SchoolManagementApp:
    """التطبيق الرئيسي لنظام إدارة المدارس"""
    
    def __init__(self):
        self.root = None
        self.current_window = None
        self.current_session = None
        self.current_language = AppConfig.DEFAULT_LANGUAGE
        
    def initialize_app(self):
        """تهيئة التطبيق"""
        try:
            # إنشاء المجلدات المطلوبة
            AppConfig.create_directories()
            
            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            if not DatabaseConfig.test_connection():
                print("إنشاء قاعدة البيانات...")
                DatabaseConfig.create_database()
                
                # تنفيذ ملف SQL لإنشاء الجداول
                sql_file = os.path.join('database', 'school_db.sql')
                if os.path.exists(sql_file):
                    DatabaseConfig.execute_sql_file(sql_file)
                else:
                    print("تحذير: لم يتم العثور على ملف قاعدة البيانات")
            
            return True
            
        except Exception as e:
            messagebox.showerror("خطأ في التهيئة", f"حدث خطأ أثناء تهيئة التطبيق:\n{str(e)}")
            return False
    
    def start_app(self):
        """بدء تشغيل التطبيق"""
        if not self.initialize_app():
            return
        
        # إنشاء النافذة الجذر
        self.root = tk.Tk()
        self.root.withdraw()  # إخفاء النافذة الجذر
        
        # تطبيق الإعدادات العامة
        self.apply_global_settings()
        
        # عرض نافذة تسجيل الدخول
        self.show_login_window()
        
        # بدء حلقة الأحداث
        self.root.mainloop()
    
    def apply_global_settings(self):
        """تطبيق الإعدادات العامة للتطبيق"""
        # إعداد الخط الافتراضي
        self.root.option_add('*Font', AppConfig.FONTS['default'])
        
        # إعداد الألوان
        style = ttk.Style()
        style.theme_use('clam')
        
        # تخصيص الألوان
        style.configure('Title.TLabel', 
                       font=AppConfig.FONTS['title'],
                       foreground=AppConfig.COLORS['primary'])
        
        style.configure('Heading.TLabel',
                       font=AppConfig.FONTS['heading'],
                       foreground=AppConfig.COLORS['dark'])
        
        style.configure('Success.TLabel',
                       foreground=AppConfig.COLORS['success'])
        
        style.configure('Error.TLabel',
                       foreground=AppConfig.COLORS['danger'])
        
        style.configure('Warning.TLabel',
                       foreground=AppConfig.COLORS['warning'])
    
    def show_login_window(self):
        """عرض نافذة تسجيل الدخول"""
        if self.current_window:
            self.current_window.destroy()
        
        self.current_window = LoginWindow(self.root, self.on_login_success)
        self.current_window.show()
    
    def show_main_window(self, user_data):
        """عرض النافذة الرئيسية"""
        if self.current_window:
            self.current_window.destroy()
        
        self.current_window = MainWindow(self.root, user_data, self.on_logout)
        self.current_window.show()
    
    def on_login_success(self, user_data):
        """معالج نجاح تسجيل الدخول"""
        # إنشاء جلسة جديدة
        session_token = session_manager.create_session(
            user_data['id'],
            user_data['user_type'],
            user_data['username']
        )
        
        self.current_session = session_token
        
        # عرض النافذة الرئيسية
        self.show_main_window(user_data)
    
    def on_logout(self):
        """معالج تسجيل الخروج"""
        if self.current_session:
            session_manager.end_session(self.current_session)
            self.current_session = None
        
        # العودة إلى نافذة تسجيل الدخول
        self.show_login_window()
    
    def change_language(self, language):
        """تغيير لغة التطبيق"""
        if language in AppConfig.SUPPORTED_LANGUAGES:
            self.current_language = language
            # إعادة تحميل النافذة الحالية باللغة الجديدة
            if self.current_window:
                self.current_window.update_language(language)
    
    def exit_app(self):
        """إغلاق التطبيق"""
        try:
            # إنهاء الجلسة الحالية
            if self.current_session:
                session_manager.end_session(self.current_session)
            
            # إغلاق النافذة
            if self.root:
                self.root.quit()
                self.root.destroy()
                
        except Exception as e:
            print(f"خطأ أثناء إغلاق التطبيق: {e}")
        finally:
            sys.exit(0)

def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء وتشغيل التطبيق
        app = SchoolManagementApp()
        app.start_app()
        
    except KeyboardInterrupt:
        print("\nتم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"خطأ غير متوقع: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ غير متوقع:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
