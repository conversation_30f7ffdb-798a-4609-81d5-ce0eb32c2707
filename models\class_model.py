# نموذج بيانات الصفوف الدراسية
from datetime import datetime
from config.database_config import Database

class ClassModel:
    """نموذج بيانات الصفوف الدراسية"""
    
    def __init__(self):
        self.db = Database()
    
    def add_class(self, class_data):
        """إضافة صف دراسي جديد"""
        try:
            if not self.db.connect():
                return False, "خطأ في الاتصال بقاعدة البيانات"
            
            # التحقق من عدم تكرار اسم الصف
            if self.class_exists(class_data['class_name'], class_data['section'], class_data['grade_level']):
                return False, "الصف موجود مسبقاً"
            
            query = """
                INSERT INTO classes (class_name, grade_level, section, capacity, 
                                   class_teacher_id, academic_year)
                VALUES (%(class_name)s, %(grade_level)s, %(section)s, %(capacity)s,
                        %(class_teacher_id)s, %(academic_year)s)
            """
            
            if self.db.execute_update(query, class_data):
                return True, "تم إضافة الصف بنجاح"
            else:
                return False, "خطأ في إضافة الصف"
                
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
        finally:
            self.db.disconnect()
    
    def update_class(self, class_id, class_data):
        """تحديث بيانات الصف"""
        try:
            if not self.db.connect():
                return False, "خطأ في الاتصال بقاعدة البيانات"
            
            query = """
                UPDATE classes SET class_name = %(class_name)s, grade_level = %(grade_level)s,
                                 section = %(section)s, capacity = %(capacity)s,
                                 class_teacher_id = %(class_teacher_id)s
                WHERE id = %(class_id)s
            """
            
            params = class_data.copy()
            params['class_id'] = class_id
            
            if self.db.execute_update(query, params):
                return True, "تم تحديث بيانات الصف بنجاح"
            else:
                return False, "خطأ في تحديث بيانات الصف"
                
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
        finally:
            self.db.disconnect()
    
    def delete_class(self, class_id):
        """حذف صف دراسي"""
        try:
            if not self.db.connect():
                return False, "خطأ في الاتصال بقاعدة البيانات"
            
            # التحقق من وجود طلاب في الصف
            student_count_query = "SELECT COUNT(*) as count FROM students WHERE class_id = %s"
            result = self.db.execute_query(student_count_query, (class_id,))
            
            if result and result[0]['count'] > 0:
                return False, "لا يمكن حذف الصف لوجود طلاب مسجلين فيه"
            
            # حذف الصف
            delete_query = "DELETE FROM classes WHERE id = %s"
            
            if self.db.execute_update(delete_query, (class_id,)):
                return True, "تم حذف الصف بنجاح"
            else:
                return False, "خطأ في حذف الصف"
                
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
        finally:
            self.db.disconnect()
    
    def get_class_by_id(self, class_id):
        """الحصول على بيانات صف بالمعرف"""
        try:
            if not self.db.connect():
                return None
            
            query = """
                SELECT c.*, u.full_name as teacher_name,
                       (SELECT COUNT(*) FROM students WHERE class_id = c.id AND status = 'active') as student_count
                FROM classes c
                LEFT JOIN teachers t ON c.class_teacher_id = t.id
                LEFT JOIN users u ON t.user_id = u.id
                WHERE c.id = %s
            """
            
            result = self.db.execute_query(query, (class_id,))
            return result[0] if result else None
            
        except Exception as e:
            print(f"خطأ في الحصول على بيانات الصف: {e}")
            return None
        finally:
            self.db.disconnect()
    
    def get_all_classes(self, academic_year=None):
        """الحصول على جميع الصفوف"""
        try:
            if not self.db.connect():
                return []
            
            query = """
                SELECT c.*, u.full_name as teacher_name,
                       (SELECT COUNT(*) FROM students WHERE class_id = c.id AND status = 'active') as student_count
                FROM classes c
                LEFT JOIN teachers t ON c.class_teacher_id = t.id
                LEFT JOIN users u ON t.user_id = u.id
            """
            
            params = []
            if academic_year:
                query += " WHERE c.academic_year = %s"
                params.append(academic_year)
            
            query += " ORDER BY c.grade_level, c.class_name, c.section"
            
            return self.db.execute_query(query, params) or []
            
        except Exception as e:
            print(f"خطأ في الحصول على قائمة الصفوف: {e}")
            return []
        finally:
            self.db.disconnect()
    
    def get_classes_by_grade(self, grade_level):
        """الحصول على الصفوف حسب المستوى"""
        try:
            if not self.db.connect():
                return []
            
            query = """
                SELECT c.*, u.full_name as teacher_name,
                       (SELECT COUNT(*) FROM students WHERE class_id = c.id AND status = 'active') as student_count
                FROM classes c
                LEFT JOIN teachers t ON c.class_teacher_id = t.id
                LEFT JOIN users u ON t.user_id = u.id
                WHERE c.grade_level = %s
                ORDER BY c.class_name, c.section
            """
            
            return self.db.execute_query(query, (grade_level,)) or []
            
        except Exception as e:
            print(f"خطأ في الحصول على صفوف المستوى: {e}")
            return []
        finally:
            self.db.disconnect()
    
    def get_available_teachers(self):
        """الحصول على المعلمين المتاحين لإدارة الصفوف"""
        try:
            if not self.db.connect():
                return []
            
            query = """
                SELECT t.id, u.full_name, t.specialization
                FROM teachers t
                JOIN users u ON t.user_id = u.id
                WHERE t.status = 'active' AND u.is_active = TRUE
                ORDER BY u.full_name
            """
            
            return self.db.execute_query(query) or []
            
        except Exception as e:
            print(f"خطأ في الحصول على المعلمين: {e}")
            return []
        finally:
            self.db.disconnect()
    
    def class_exists(self, class_name, section, grade_level):
        """التحقق من وجود الصف"""
        try:
            if not self.db.connect():
                return False
            
            query = """
                SELECT COUNT(*) as count 
                FROM classes 
                WHERE class_name = %s AND section = %s AND grade_level = %s
            """
            
            result = self.db.execute_query(query, (class_name, section, grade_level))
            return result[0]['count'] > 0 if result else False
            
        except Exception as e:
            print(f"خطأ في التحقق من وجود الصف: {e}")
            return False
        finally:
            self.db.disconnect()
    
    def get_class_statistics(self):
        """الحصول على إحصائيات الصفوف"""
        try:
            if not self.db.connect():
                return {}
            
            stats = {}
            
            # إجمالي الصفوف
            total_query = "SELECT COUNT(*) as total FROM classes"
            result = self.db.execute_query(total_query)
            stats['total'] = result[0]['total'] if result else 0
            
            # الصفوف حسب المستوى
            grade_query = """
                SELECT grade_level, COUNT(*) as count 
                FROM classes 
                GROUP BY grade_level 
                ORDER BY grade_level
            """
            grade_result = self.db.execute_query(grade_query)
            stats['by_grade'] = grade_result if grade_result else []
            
            # إجمالي الطلاب في كل صف
            students_query = """
                SELECT c.class_name, c.section, c.grade_level, COUNT(s.id) as student_count
                FROM classes c
                LEFT JOIN students s ON c.id = s.class_id AND s.status = 'active'
                GROUP BY c.id, c.class_name, c.section, c.grade_level
                ORDER BY c.grade_level, c.class_name, c.section
            """
            students_result = self.db.execute_query(students_query)
            stats['students_per_class'] = students_result if students_result else []
            
            # الصفوف التي تحتاج معلم
            no_teacher_query = """
                SELECT COUNT(*) as count 
                FROM classes 
                WHERE class_teacher_id IS NULL
            """
            no_teacher_result = self.db.execute_query(no_teacher_query)
            stats['classes_without_teacher'] = no_teacher_result[0]['count'] if no_teacher_result else 0
            
            return stats
            
        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات الصفوف: {e}")
            return {}
        finally:
            self.db.disconnect()
    
    def assign_teacher_to_class(self, class_id, teacher_id):
        """تعيين معلم لصف"""
        try:
            if not self.db.connect():
                return False, "خطأ في الاتصال بقاعدة البيانات"
            
            query = "UPDATE classes SET class_teacher_id = %s WHERE id = %s"
            
            if self.db.execute_update(query, (teacher_id, class_id)):
                return True, "تم تعيين المعلم للصف بنجاح"
            else:
                return False, "خطأ في تعيين المعلم"
                
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
        finally:
            self.db.disconnect()
    
    def get_class_students(self, class_id):
        """الحصول على طلاب الصف"""
        try:
            if not self.db.connect():
                return []
            
            query = """
                SELECT s.*, u.full_name, u.email, u.phone
                FROM students s
                JOIN users u ON s.user_id = u.id
                WHERE s.class_id = %s AND s.status = 'active'
                ORDER BY u.full_name
            """
            
            return self.db.execute_query(query, (class_id,)) or []
            
        except Exception as e:
            print(f"خطأ في الحصول على طلاب الصف: {e}")
            return []
        finally:
            self.db.disconnect()
