#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة المدارس
School Management System Test
"""

import sys
import os
import unittest
from datetime import date

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestSystemComponents(unittest.TestCase):
    """اختبار مكونات النظام الأساسية"""
    
    def test_imports(self):
        """اختبار استيراد الوحدات الأساسية"""
        try:
            from config.database_config import DatabaseConfig, Database
            from config.app_config import AppConfig, LanguageConfig
            from utils.security import SecurityManager, SessionManager, PermissionManager
            from models.student import Student
            from models.teacher import Teacher
            from models.class_model import ClassModel
            print("✓ جميع الوحدات الأساسية تم استيرادها بنجاح")
        except ImportError as e:
            self.fail(f"فشل في استيراد الوحدات: {e}")
    
    def test_database_config(self):
        """اختبار إعدادات قاعدة البيانات"""
        from config.database_config import DatabaseConfig
        
        # التحقق من وجود الإعدادات المطلوبة
        required_keys = ['host', 'database', 'user', 'password', 'charset']
        for key in required_keys:
            self.assertIn(key, DatabaseConfig.DB_CONFIG)
        
        print("✓ إعدادات قاعدة البيانات صحيحة")
    
    def test_app_config(self):
        """اختبار إعدادات التطبيق"""
        from config.app_config import AppConfig
        
        # التحقق من الإعدادات الأساسية
        self.assertIsInstance(AppConfig.APP_NAME, str)
        self.assertIsInstance(AppConfig.APP_VERSION, str)
        self.assertIsInstance(AppConfig.WINDOW_WIDTH, int)
        self.assertIsInstance(AppConfig.WINDOW_HEIGHT, int)
        
        print("✓ إعدادات التطبيق صحيحة")
    
    def test_security_manager(self):
        """اختبار مدير الأمان"""
        from utils.security import SecurityManager
        
        # اختبار تشفير كلمة المرور
        password = "test123"
        hashed = SecurityManager.hash_password(password)
        self.assertIsInstance(hashed, str)
        self.assertIn(':', hashed)  # يجب أن يحتوي على salt
        
        # اختبار التحقق من كلمة المرور
        self.assertTrue(SecurityManager.verify_password(password, hashed))
        self.assertFalse(SecurityManager.verify_password("wrong", hashed))
        
        # اختبار التحقق من قوة كلمة المرور
        valid, errors = SecurityManager.validate_password("123456")
        self.assertIsInstance(valid, bool)
        self.assertIsInstance(errors, list)
        
        print("✓ مدير الأمان يعمل بشكل صحيح")
    
    def test_permission_manager(self):
        """اختبار مدير الصلاحيات"""
        from utils.security import PermissionManager
        
        # اختبار الصلاحيات
        self.assertTrue(PermissionManager.has_permission('admin', 'manage_users'))
        self.assertTrue(PermissionManager.has_permission('teacher', 'manage_attendance'))
        self.assertFalse(PermissionManager.has_permission('student', 'manage_users'))
        
        # اختبار الحصول على صلاحيات المستخدم
        admin_permissions = PermissionManager.get_user_permissions('admin')
        self.assertIsInstance(admin_permissions, list)
        self.assertGreater(len(admin_permissions), 0)
        
        print("✓ مدير الصلاحيات يعمل بشكل صحيح")
    
    def test_language_config(self):
        """اختبار إعدادات اللغة"""
        from config.app_config import LanguageConfig
        
        # اختبار الترجمة
        arabic_text = LanguageConfig.get_text('students', 'ar')
        french_text = LanguageConfig.get_text('students', 'fr')
        
        self.assertEqual(arabic_text, 'الطلاب')
        self.assertEqual(french_text, 'Étudiants')
        
        print("✓ إعدادات اللغة تعمل بشكل صحيح")

class TestDatabaseConnection(unittest.TestCase):
    """اختبار الاتصال بقاعدة البيانات"""
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        from config.database_config import DatabaseConfig
        
        # محاولة الاتصال
        connection_ok = DatabaseConfig.test_connection()
        
        if connection_ok:
            print("✓ الاتصال بقاعدة البيانات ناجح")
        else:
            print("⚠ لا يمكن الاتصال بقاعدة البيانات - تأكد من تشغيل MySQL وصحة الإعدادات")

class TestModels(unittest.TestCase):
    """اختبار نماذج البيانات"""
    
    def test_student_model(self):
        """اختبار نموذج الطالب"""
        from models.student import Student
        
        student_model = Student()
        self.assertIsNotNone(student_model)
        
        # اختبار التحقق من وجود رقم طالب (بدون اتصال فعلي)
        # هذا مجرد اختبار أن الدالة موجودة ولا تسبب خطأ
        try:
            exists = student_model.student_id_exists("TEST001")
            self.assertIsInstance(exists, bool)
        except:
            pass  # قد يفشل بسبب عدم وجود اتصال بقاعدة البيانات
        
        print("✓ نموذج الطالب تم إنشاؤه بنجاح")
    
    def test_teacher_model(self):
        """اختبار نموذج المعلم"""
        from models.teacher import Teacher
        
        teacher_model = Teacher()
        self.assertIsNotNone(teacher_model)
        
        print("✓ نموذج المعلم تم إنشاؤه بنجاح")
    
    def test_class_model(self):
        """اختبار نموذج الصف"""
        from models.class_model import ClassModel
        
        class_model = ClassModel()
        self.assertIsNotNone(class_model)
        
        print("✓ نموذج الصف تم إنشاؤه بنجاح")

def run_system_check():
    """تشغيل فحص شامل للنظام"""
    print("=" * 60)
    print("فحص نظام إدارة المدارس")
    print("School Management System Check")
    print("=" * 60)
    
    # فحص Python
    print(f"إصدار Python: {sys.version}")
    
    # فحص المكتبات المطلوبة
    required_modules = [
        ('mysql.connector', 'MySQL Connector'),
        ('PIL', 'Pillow'),
        ('tkcalendar', 'TkCalendar'),
        ('reportlab', 'ReportLab'),
        ('openpyxl', 'OpenPyXL')
    ]
    
    print("\nفحص المكتبات المطلوبة:")
    missing_modules = []
    
    for module, name in required_modules:
        try:
            __import__(module)
            print(f"✓ {name}")
        except ImportError:
            print(f"✗ {name} - غير مثبت")
            missing_modules.append(name)
    
    if missing_modules:
        print(f"\nالمكتبات المفقودة: {', '.join(missing_modules)}")
        print("لتثبيت المكتبات المفقودة: pip install -r requirements.txt")
    
    # فحص الملفات المطلوبة
    print("\nفحص الملفات المطلوبة:")
    required_files = [
        'main.py',
        'config/database_config.py',
        'config/app_config.py',
        'database/school_db.sql',
        'utils/security.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - غير موجود")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\nالملفات المفقودة: {', '.join(missing_files)}")
    
    # تشغيل الاختبارات
    print("\n" + "=" * 60)
    print("تشغيل الاختبارات:")
    print("=" * 60)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات المكونات
    test_suite.addTest(unittest.makeSuite(TestSystemComponents))
    
    # إضافة اختبار قاعدة البيانات
    test_suite.addTest(unittest.makeSuite(TestDatabaseConnection))
    
    # إضافة اختبارات النماذج
    test_suite.addTest(unittest.makeSuite(TestModels))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ جميع الاختبارات نجحت! النظام جاهز للاستخدام")
    else:
        print("⚠ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)

def main():
    """الدالة الرئيسية"""
    if len(sys.argv) > 1 and sys.argv[1] == '--unittest':
        # تشغيل الاختبارات فقط
        unittest.main(argv=[''], exit=False)
    else:
        # تشغيل الفحص الشامل
        run_system_check()

if __name__ == "__main__":
    main()
