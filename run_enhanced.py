#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل محسن لنظام إدارة المدارس
Enhanced School Management System Runner
"""

import sys
import os
import subprocess
import platform
from pathlib import Path
import tkinter as tk
from tkinter import messagebox, ttk

class EnhancedSystemRunner:
    """فئة تشغيل النظام المحسنة"""
    
    def __init__(self):
        self.system = platform.system()
        self.project_root = Path(__file__).parent
        self.root = None
        
    def create_gui(self):
        """إنشاء واجهة المستخدم الرسومية"""
        self.root = tk.Tk()
        self.root.title("نظام إدارة المدارس - معالج التشغيل")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # العنوان
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🏫 نظام إدارة المدارس", 
                              font=('Arial', 18, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(title_frame, text="School Management System", 
                                 font=('Arial', 12), fg='#ecf0f1', bg='#2c3e50')
        subtitle_label.pack()
        
        # منطقة المحتوى
        content_frame = tk.Frame(self.root, bg='white', padx=20, pady=20)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # معلومات النظام
        info_frame = ttk.LabelFrame(content_frame, text="معلومات النظام", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(info_frame, text=f"نظام التشغيل: {self.system}").pack(anchor=tk.W)
        tk.Label(info_frame, text=f"إصدار Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}").pack(anchor=tk.W)
        tk.Label(info_frame, text=f"مسار المشروع: {self.project_root}").pack(anchor=tk.W)
        
        # منطقة السجل
        log_frame = ttk.LabelFrame(content_frame, text="سجل العمليات", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.log_text = tk.Text(log_frame, height=12, width=70, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # شريط التقدم
        self.progress = ttk.Progressbar(content_frame, mode='determinate', length=400)
        self.progress.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار التحكم
        buttons_frame = tk.Frame(content_frame)
        buttons_frame.pack(fill=tk.X)
        
        self.start_button = tk.Button(buttons_frame, text="🚀 بدء التشغيل", 
                                     command=self.start_system, bg='#27ae60', fg='white',
                                     font=('Arial', 10, 'bold'), padx=20)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.setup_button = tk.Button(buttons_frame, text="⚙️ إعداد قاعدة البيانات", 
                                     command=self.setup_database, bg='#f39c12', fg='white',
                                     font=('Arial', 10, 'bold'), padx=20)
        self.setup_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_button = tk.Button(buttons_frame, text="🧪 اختبار النظام", 
                                    command=self.test_system, bg='#3498db', fg='white',
                                    font=('Arial', 10, 'bold'), padx=20)
        self.test_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.exit_button = tk.Button(buttons_frame, text="❌ إغلاق", 
                                    command=self.root.quit, bg='#e74c3c', fg='white',
                                    font=('Arial', 10, 'bold'), padx=20)
        self.exit_button.pack(side=tk.RIGHT)
        
        # توسيط النافذة
        self.center_window()
        
        # رسالة ترحيبية
        self.log("مرحباً بك في معالج تشغيل نظام إدارة المدارس")
        self.log("يرجى الضغط على 'بدء التشغيل' لفحص النظام وتشغيله")
        
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def log(self, message, level="INFO"):
        """إضافة رسالة إلى السجل"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if level == "ERROR":
            prefix = "❌"
        elif level == "WARNING":
            prefix = "⚠️"
        elif level == "SUCCESS":
            prefix = "✅"
        else:
            prefix = "ℹ️"
        
        log_message = f"[{timestamp}] {prefix} {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
    
    def update_progress(self, value):
        """تحديث شريط التقدم"""
        self.progress['value'] = value
        self.root.update()
    
    def check_python_version(self):
        """فحص إصدار Python"""
        self.log("فحص إصدار Python...")
        
        if sys.version_info < (3, 8):
            self.log(f"إصدار Python غير مناسب: {sys.version_info.major}.{sys.version_info.minor}", "ERROR")
            self.log("يتطلب Python 3.8 أو أحدث", "ERROR")
            return False
        
        self.log(f"إصدار Python مناسب: {sys.version_info.major}.{sys.version_info.minor}", "SUCCESS")
        return True
    
    def check_required_files(self):
        """فحص الملفات المطلوبة"""
        self.log("فحص الملفات المطلوبة...")
        
        required_files = [
            'main.py',
            'config/database_config.py',
            'config/app_config.py',
            'database/school_db.sql',
            'requirements.txt'
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.log(f"✓ {file_path}")
            else:
                self.log(f"✗ {file_path} - غير موجود", "ERROR")
                missing_files.append(file_path)
        
        if missing_files:
            self.log(f"الملفات المفقودة: {', '.join(missing_files)}", "ERROR")
            return False
        
        self.log("جميع الملفات المطلوبة موجودة", "SUCCESS")
        return True
    
    def check_and_install_requirements(self):
        """فحص وتثبيت المتطلبات"""
        self.log("فحص المكتبات المطلوبة...")
        
        required_packages = {
            'mysql.connector': 'mysql-connector-python',
            'PIL': 'Pillow',
            'tkcalendar': 'tkcalendar',
            'reportlab': 'reportlab',
            'openpyxl': 'openpyxl'
        }
        
        missing_packages = []
        
        for import_name, package_name in required_packages.items():
            try:
                if import_name == 'mysql.connector':
                    import mysql.connector
                elif import_name == 'PIL':
                    from PIL import Image
                else:
                    __import__(import_name)
                self.log(f"✓ {package_name}")
            except ImportError:
                self.log(f"✗ {package_name} - غير مثبت", "WARNING")
                missing_packages.append(package_name)
        
        if missing_packages:
            self.log(f"المكتبات المفقودة: {', '.join(missing_packages)}", "WARNING")
            
            # سؤال المستخدم عن التثبيت
            response = messagebox.askyesno("تثبيت المكتبات", 
                                         f"هناك {len(missing_packages)} مكتبة مفقودة.\nهل تريد تثبيتها تلقائياً؟")
            
            if response:
                return self.install_packages(missing_packages)
            else:
                self.log("تم تخطي تثبيت المكتبات", "WARNING")
                return False
        
        self.log("جميع المكتبات المطلوبة مثبتة", "SUCCESS")
        return True
    
    def install_packages(self, packages):
        """تثبيت المكتبات المفقودة"""
        self.log("بدء تثبيت المكتبات...")
        
        try:
            # تحديث pip
            self.log("تحديث pip...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # تثبيت المكتبات
            total_packages = len(packages)
            for i, package in enumerate(packages):
                self.log(f"تثبيت {package}...")
                self.update_progress((i / total_packages) * 100)
                
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package],
                                    stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                self.log(f"تم تثبيت {package}", "SUCCESS")
            
            self.update_progress(100)
            self.log("تم تثبيت جميع المكتبات بنجاح", "SUCCESS")
            return True
            
        except subprocess.CalledProcessError as e:
            self.log(f"خطأ في تثبيت المكتبات: {e}", "ERROR")
            messagebox.showerror("خطأ", "فشل في تثبيت المكتبات\nيرجى تثبيتها يدوياً")
            return False
    
    def check_database_connection(self):
        """فحص الاتصال بقاعدة البيانات"""
        self.log("فحص قاعدة البيانات...")
        
        try:
            from config.database_config import DatabaseConfig
            if DatabaseConfig.test_connection():
                self.log("الاتصال بقاعدة البيانات ناجح", "SUCCESS")
                return True
            else:
                self.log("لا يمكن الاتصال بقاعدة البيانات", "WARNING")
                return False
        except Exception as e:
            self.log(f"خطأ في فحص قاعدة البيانات: {e}", "WARNING")
            return False
    
    def start_system(self):
        """بدء تشغيل النظام"""
        self.log("بدء فحص النظام...")
        self.start_button.config(state='disabled')
        
        # إعادة تعيين شريط التقدم
        self.update_progress(0)
        
        # قائمة الفحوصات
        checks = [
            ("فحص إصدار Python", self.check_python_version, True),
            ("فحص الملفات المطلوبة", self.check_required_files, True),
            ("فحص وتثبيت المكتبات", self.check_and_install_requirements, True),
            ("فحص قاعدة البيانات", self.check_database_connection, False)
        ]
        
        # تشغيل الفحوصات
        total_checks = len(checks)
        critical_failed = False
        db_failed = False
        
        for i, (check_name, check_function, is_critical) in enumerate(checks):
            self.log(f"تشغيل: {check_name}")
            self.update_progress((i / total_checks) * 80)
            
            try:
                result = check_function()
                if not result:
                    if is_critical:
                        critical_failed = True
                        break
                    elif check_name == "فحص قاعدة البيانات":
                        db_failed = True
            except Exception as e:
                self.log(f"خطأ في {check_name}: {e}", "ERROR")
                if is_critical:
                    critical_failed = True
                    break
        
        self.update_progress(80)
        
        # التعامل مع النتائج
        if critical_failed:
            self.log("فشل في الفحوصات الأساسية", "ERROR")
            messagebox.showerror("خطأ", "لا يمكن تشغيل النظام بسبب فشل الفحوصات الأساسية")
            self.start_button.config(state='normal')
            return
        
        if db_failed:
            response = messagebox.askyesno("قاعدة البيانات", 
                                         "هناك مشكلة في قاعدة البيانات.\nهل تريد المتابعة؟")
            if not response:
                self.start_button.config(state='normal')
                return
        
        # تشغيل النظام الرئيسي
        self.log("تشغيل النظام الرئيسي...")
        self.update_progress(90)
        
        try:
            # إخفاء النافذة الحالية
            self.root.withdraw()
            
            # تشغيل النظام
            from main import main as run_main
            run_main()
            
            # إظهار النافذة مرة أخرى بعد إغلاق النظام
            self.root.deiconify()
            self.log("تم إغلاق النظام", "SUCCESS")
            
        except Exception as e:
            self.root.deiconify()
            self.log(f"خطأ في تشغيل النظام: {e}", "ERROR")
            messagebox.showerror("خطأ", f"فشل في تشغيل النظام:\n{e}")
        
        self.update_progress(100)
        self.start_button.config(state='normal')
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        self.log("تشغيل معالج إعداد قاعدة البيانات...")
        
        try:
            setup_file = self.project_root / 'setup_database.py'
            if setup_file.exists():
                subprocess.run([sys.executable, str(setup_file)])
                self.log("تم إكمال معالج إعداد قاعدة البيانات", "SUCCESS")
            else:
                self.log("ملف إعداد قاعدة البيانات غير موجود", "ERROR")
                messagebox.showerror("خطأ", "ملف setup_database.py غير موجود")
        except Exception as e:
            self.log(f"خطأ في تشغيل معالج الإعداد: {e}", "ERROR")
            messagebox.showerror("خطأ", f"فشل في تشغيل معالج الإعداد:\n{e}")
    
    def test_system(self):
        """اختبار النظام"""
        self.log("تشغيل اختبارات النظام...")
        
        try:
            test_file = self.project_root / 'test_system.py'
            if test_file.exists():
                result = subprocess.run([sys.executable, str(test_file)], 
                                      capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    self.log("جميع الاختبارات نجحت", "SUCCESS")
                    messagebox.showinfo("اختبار النظام", "جميع الاختبارات نجحت!")
                else:
                    self.log("بعض الاختبارات فشلت", "WARNING")
                    messagebox.showwarning("اختبار النظام", "بعض الاختبارات فشلت")
            else:
                self.log("ملف الاختبارات غير موجود", "WARNING")
                messagebox.showwarning("اختبار النظام", "ملف test_system.py غير موجود")
                
        except subprocess.TimeoutExpired:
            self.log("انتهت مهلة الاختبار", "WARNING")
            messagebox.showwarning("اختبار النظام", "انتهت مهلة الاختبار")
        except Exception as e:
            self.log(f"خطأ في تشغيل الاختبارات: {e}", "ERROR")
            messagebox.showerror("خطأ", f"فشل في تشغيل الاختبارات:\n{e}")
    
    def run(self):
        """تشغيل الواجهة الرسومية"""
        self.create_gui()
        self.root.mainloop()

def main():
    """نقطة الدخول الرئيسية"""
    runner = EnhancedSystemRunner()
    runner.run()

if __name__ == "__main__":
    main()
