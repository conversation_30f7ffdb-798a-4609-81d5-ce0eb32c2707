# نموذج بيانات الطالب
from datetime import datetime, date
from config.database_config import Database
from utils.security import SecurityManager

class Student:
    """نموذج بيانات الطالب"""
    
    def __init__(self):
        self.db = Database()
    
    def add_student(self, student_data):
        """إضافة طالب جديد"""
        try:
            if not self.db.connect():
                return False, "خطأ في الاتصال بقاعدة البيانات"
            
            # التحقق من عدم تكرار رقم الطالب
            if self.student_id_exists(student_data['student_id']):
                return False, "رقم الطالب موجود مسبقاً"
            
            # إنشاء حساب مستخدم للطالب
            user_data = {
                'username': student_data['student_id'],
                'password_hash': SecurityManager.hash_password(student_data.get('password', '123456')),
                'user_type': 'student',
                'full_name': student_data['full_name'],
                'email': student_data.get('email', ''),
                'phone': student_data.get('phone', ''),
                'address': student_data.get('address', '')
            }
            
            # إدراج بيانات المستخدم
            user_query = """
                INSERT INTO users (username, password_hash, user_type, full_name, email, phone, address)
                VALUES (%(username)s, %(password_hash)s, %(user_type)s, %(full_name)s, %(email)s, %(phone)s, %(address)s)
            """
            
            if not self.db.execute_update(user_query, user_data):
                return False, "خطأ في إنشاء حساب المستخدم"
            
            user_id = self.db.get_last_insert_id()
            
            # إدراج بيانات الطالب
            student_query = """
                INSERT INTO students (user_id, student_id, class_id, parent_id, date_of_birth, 
                                    gender, blood_type, medical_notes, enrollment_date)
                VALUES (%(user_id)s, %(student_id)s, %(class_id)s, %(parent_id)s, %(date_of_birth)s,
                        %(gender)s, %(blood_type)s, %(medical_notes)s, %(enrollment_date)s)
            """
            
            student_params = {
                'user_id': user_id,
                'student_id': student_data['student_id'],
                'class_id': student_data.get('class_id'),
                'parent_id': student_data.get('parent_id'),
                'date_of_birth': student_data.get('date_of_birth'),
                'gender': student_data['gender'],
                'blood_type': student_data.get('blood_type', ''),
                'medical_notes': student_data.get('medical_notes', ''),
                'enrollment_date': student_data.get('enrollment_date', date.today())
            }
            
            if self.db.execute_update(student_query, student_params):
                return True, "تم إضافة الطالب بنجاح"
            else:
                # حذف المستخدم في حالة فشل إضافة الطالب
                self.db.execute_update("DELETE FROM users WHERE id = %s", (user_id,))
                return False, "خطأ في إضافة بيانات الطالب"
                
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
        finally:
            self.db.disconnect()
    
    def update_student(self, student_id, student_data):
        """تحديث بيانات الطالب"""
        try:
            if not self.db.connect():
                return False, "خطأ في الاتصال بقاعدة البيانات"
            
            # الحصول على معرف المستخدم
            user_id_query = "SELECT user_id FROM students WHERE student_id = %s"
            result = self.db.execute_query(user_id_query, (student_id,))
            
            if not result:
                return False, "الطالب غير موجود"
            
            user_id = result[0]['user_id']
            
            # تحديث بيانات المستخدم
            user_query = """
                UPDATE users SET full_name = %(full_name)s, email = %(email)s, 
                               phone = %(phone)s, address = %(address)s
                WHERE id = %(user_id)s
            """
            
            user_params = {
                'user_id': user_id,
                'full_name': student_data['full_name'],
                'email': student_data.get('email', ''),
                'phone': student_data.get('phone', ''),
                'address': student_data.get('address', '')
            }
            
            if not self.db.execute_update(user_query, user_params):
                return False, "خطأ في تحديث بيانات المستخدم"
            
            # تحديث بيانات الطالب
            student_query = """
                UPDATE students SET class_id = %(class_id)s, parent_id = %(parent_id)s,
                                  date_of_birth = %(date_of_birth)s, gender = %(gender)s,
                                  blood_type = %(blood_type)s, medical_notes = %(medical_notes)s
                WHERE student_id = %(student_id)s
            """
            
            student_params = {
                'student_id': student_id,
                'class_id': student_data.get('class_id'),
                'parent_id': student_data.get('parent_id'),
                'date_of_birth': student_data.get('date_of_birth'),
                'gender': student_data['gender'],
                'blood_type': student_data.get('blood_type', ''),
                'medical_notes': student_data.get('medical_notes', '')
            }
            
            if self.db.execute_update(student_query, student_params):
                return True, "تم تحديث بيانات الطالب بنجاح"
            else:
                return False, "خطأ في تحديث بيانات الطالب"
                
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
        finally:
            self.db.disconnect()
    
    def delete_student(self, student_id):
        """حذف طالب"""
        try:
            if not self.db.connect():
                return False, "خطأ في الاتصال بقاعدة البيانات"
            
            # الحصول على معرف المستخدم
            user_id_query = "SELECT user_id FROM students WHERE student_id = %s"
            result = self.db.execute_query(user_id_query, (student_id,))
            
            if not result:
                return False, "الطالب غير موجود"
            
            user_id = result[0]['user_id']
            
            # حذف الطالب (سيتم حذف المستخدم تلقائياً بسبب CASCADE)
            delete_query = "DELETE FROM students WHERE student_id = %s"
            
            if self.db.execute_update(delete_query, (student_id,)):
                return True, "تم حذف الطالب بنجاح"
            else:
                return False, "خطأ في حذف الطالب"
                
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
        finally:
            self.db.disconnect()
    
    def get_student_by_id(self, student_id):
        """الحصول على بيانات طالب بالرقم"""
        try:
            if not self.db.connect():
                return None
            
            query = """
                SELECT s.*, u.full_name, u.email, u.phone, u.address, u.is_active,
                       c.class_name, c.grade_level, c.section,
                       p.full_name as parent_name
                FROM students s
                JOIN users u ON s.user_id = u.id
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN users p ON s.parent_id = p.id
                WHERE s.student_id = %s
            """
            
            result = self.db.execute_query(query, (student_id,))
            return result[0] if result else None
            
        except Exception as e:
            print(f"خطأ في الحصول على بيانات الطالب: {e}")
            return None
        finally:
            self.db.disconnect()
    
    def get_all_students(self, class_id=None, status='active'):
        """الحصول على جميع الطلاب"""
        try:
            if not self.db.connect():
                return []
            
            query = """
                SELECT s.*, u.full_name, u.email, u.phone, u.is_active,
                       c.class_name, c.grade_level, c.section,
                       p.full_name as parent_name
                FROM students s
                JOIN users u ON s.user_id = u.id
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN users p ON s.parent_id = p.id
                WHERE s.status = %s
            """
            
            params = [status]
            
            if class_id:
                query += " AND s.class_id = %s"
                params.append(class_id)
            
            query += " ORDER BY u.full_name"
            
            return self.db.execute_query(query, params) or []
            
        except Exception as e:
            print(f"خطأ في الحصول على قائمة الطلاب: {e}")
            return []
        finally:
            self.db.disconnect()
    
    def search_students(self, search_term):
        """البحث في الطلاب"""
        try:
            if not self.db.connect():
                return []
            
            query = """
                SELECT s.*, u.full_name, u.email, u.phone,
                       c.class_name, c.grade_level, c.section
                FROM students s
                JOIN users u ON s.user_id = u.id
                LEFT JOIN classes c ON s.class_id = c.id
                WHERE (u.full_name LIKE %s OR s.student_id LIKE %s)
                AND s.status = 'active'
                ORDER BY u.full_name
            """
            
            search_pattern = f"%{search_term}%"
            return self.db.execute_query(query, (search_pattern, search_pattern)) or []
            
        except Exception as e:
            print(f"خطأ في البحث عن الطلاب: {e}")
            return []
        finally:
            self.db.disconnect()
    
    def student_id_exists(self, student_id):
        """التحقق من وجود رقم الطالب"""
        try:
            if not self.db.connect():
                return False
            
            query = "SELECT COUNT(*) as count FROM students WHERE student_id = %s"
            result = self.db.execute_query(query, (student_id,))
            
            return result[0]['count'] > 0 if result else False
            
        except Exception as e:
            print(f"خطأ في التحقق من رقم الطالب: {e}")
            return False
        finally:
            self.db.disconnect()
    
    def get_student_statistics(self):
        """الحصول على إحصائيات الطلاب"""
        try:
            if not self.db.connect():
                return {}
            
            stats = {}
            
            # إجمالي الطلاب
            total_query = "SELECT COUNT(*) as total FROM students WHERE status = 'active'"
            result = self.db.execute_query(total_query)
            stats['total'] = result[0]['total'] if result else 0
            
            # الطلاب حسب الجنس
            gender_query = """
                SELECT gender, COUNT(*) as count 
                FROM students 
                WHERE status = 'active' 
                GROUP BY gender
            """
            gender_result = self.db.execute_query(gender_query)
            stats['by_gender'] = {row['gender']: row['count'] for row in gender_result} if gender_result else {}
            
            # الطلاب حسب الصف
            class_query = """
                SELECT c.class_name, c.grade_level, COUNT(s.id) as count
                FROM classes c
                LEFT JOIN students s ON c.id = s.class_id AND s.status = 'active'
                GROUP BY c.id, c.class_name, c.grade_level
                ORDER BY c.grade_level, c.class_name
            """
            class_result = self.db.execute_query(class_query)
            stats['by_class'] = class_result if class_result else []
            
            return stats
            
        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات الطلاب: {e}")
            return {}
        finally:
            self.db.disconnect()
