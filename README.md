# نظام إدارة المدارس - School Management System

نظام شامل لإدارة المدارس مطور باستخدام Python مع Tkinter وMySQL، يوفر جميع الأدوات اللازمة لإدارة العملية التعليمية بكفاءة عالية.

## المميزات الرئيسية

### ✅ إدارة المستخدمين
- **إدارة الطلاب**: إضافة، تعديل، حذف، والبحث في بيانات الطلاب
- **إدارة المعلمين**: إدارة شاملة لبيانات المعلمين وتخصصاتهم
- **إدارة أولياء الأمور**: ربط الطلاب بأولياء أمورهم
- **نظام صلاحيات متقدم**: مستويات مختلفة من الصلاحيات (مدير، معلم، طالب، ولي أمر)

### ✅ الإدارة الأكاديمية
- **إدارة الصفوف**: تنظيم الصفوف والأقسام حسب المستويات
- **الجداول الدراسية**: إعداد وإدارة الجداول الدراسية
- **إدارة المواد**: تنظيم المواد الدراسية وربطها بالمعلمين
- **الامتحانات والدرجات**: إدارة الامتحانات وإدخال الدرجات

### ✅ المتابعة والتقييم
- **الحضور والغياب**: تسجيل ومتابعة حضور الطلاب والمعلمين
- **التقارير الشاملة**: تقارير مفصلة عن الأداء والحضور
- **الإحصائيات**: لوحة تحكم بالإحصائيات المهمة

### ✅ الإدارة المالية
- **الفواتير**: إصدار فواتير الرسوم الدراسية
- **المدفوعات**: متابعة المدفوعات والأقساط
- **التقارير المالية**: تقارير مالية شاملة

### ✅ التواصل والإشعارات
- **نظام الإشعارات**: إرسال إشعارات لأولياء الأمور
- **واجهات منفصلة**: واجهات مخصصة للطلاب وأولياء الأمور
- **متعدد اللغات**: دعم اللغة العربية والفرنسية

## متطلبات النظام

### البرمجيات المطلوبة
- Python 3.8 أو أحدث
- MySQL Server 8.0 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

### المكتبات المطلوبة
```
tkinter (مدمجة مع Python)
mysql-connector-python==8.2.0
Pillow==10.1.0
tkcalendar==1.6.1
reportlab==4.0.7
openpyxl==3.1.2
```

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd school-management-system
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات
1. تأكد من تشغيل MySQL Server
2. قم بتعديل إعدادات قاعدة البيانات في `config/database_config.py`
3. سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول

### 4. تشغيل البرنامج
```bash
python run.py
```
أو
```bash
python main.py
```

## بيانات الدخول الافتراضية

**المدير الرئيسي:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## هيكل المشروع

```
school-management-system/
├── main.py                 # الملف الرئيسي
├── run.py                  # ملف التشغيل مع فحص المتطلبات
├── requirements.txt        # قائمة المكتبات المطلوبة
├── README.md              # دليل المشروع
├── config/                # ملفات الإعدادات
│   ├── __init__.py
│   ├── app_config.py      # إعدادات التطبيق
│   └── database_config.py # إعدادات قاعدة البيانات
├── database/              # ملفات قاعدة البيانات
│   └── school_db.sql      # هيكل قاعدة البيانات
├── gui/                   # واجهات المستخدم
│   ├── __init__.py
│   ├── login_window.py    # نافذة تسجيل الدخول
│   ├── main_window.py     # النافذة الرئيسية
│   └── students_window.py # واجهة إدارة الطلاب
├── models/                # نماذج البيانات
│   ├── __init__.py
│   ├── student.py         # نموذج الطالب
│   └── class_model.py     # نموذج الصف
├── utils/                 # الأدوات المساعدة
│   └── security.py        # أدوات الأمان والتشفير
├── assets/                # الملفات المساعدة
│   ├── images/           # الصور
│   └── icons/            # الأيقونات
├── reports/              # التقارير المُصدرة
├── backups/              # النسخ الاحتياطية
└── logs/                 # ملفات السجلات
```

## الاستخدام

### تسجيل الدخول
1. شغل البرنامج باستخدام `python run.py`
2. أدخل بيانات الدخول (admin/admin123 للمدير)
3. اختر اللغة المفضلة (عربي/فرنسي)

### إدارة الطلاب
1. من القائمة الرئيسية، اختر "الطلاب"
2. يمكنك إضافة طالب جديد، تعديل بيانات طالب موجود، أو حذف طالب
3. استخدم خاصية البحث للعثور على طالب معين

### إدارة الصفوف
1. اختر "الصفوف" من القائمة الرئيسية
2. أضف صفوف جديدة وحدد المعلم المسؤول
3. اعرض قائمة الطلاب في كل صف

## الأمان

- **تشفير كلمات المرور**: جميع كلمات المرور مشفرة باستخدام SHA-256 مع Salt
- **نظام الجلسات**: إدارة آمنة للجلسات مع انتهاء صلاحية تلقائي
- **مستويات الصلاحيات**: تحكم دقيق في صلاحيات كل نوع مستخدم
- **حماية من الهجمات**: حماية من محاولات الدخول المتكررة

## التطوير والمساهمة

### إضافة وحدة جديدة
1. أنشئ نموذج البيانات في مجلد `models/`
2. أنشئ واجهة المستخدم في مجلد `gui/`
3. أضف الوحدة إلى النافذة الرئيسية

### قاعدة البيانات
- استخدم `Database` class للتفاعل مع قاعدة البيانات
- جميع الاستعلامات محمية من SQL Injection
- استخدم المعاملات (Parameters) دائماً

## الدعم الفني

### المشاكل الشائعة

**خطأ في الاتصال بقاعدة البيانات:**
- تأكد من تشغيل MySQL Server
- تحقق من إعدادات الاتصال في `config/database_config.py`

**مكتبات مفقودة:**
- شغل `pip install -r requirements.txt`
- أو استخدم `python run.py` للتثبيت التلقائي

**مشاكل في الواجهة:**
- تأكد من تثبيت مكتبة Tkinter
- في Ubuntu: `sudo apt-get install python3-tk`

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

## المطورون

- فريق تطوير نظام إدارة المدارس
- للدعم الفني: [البريد الإلكتروني]

## الإصدارات

### الإصدار 1.0.0
- النظام الأساسي لإدارة الطلاب والمعلمين
- نظام تسجيل الدخول والأمان
- واجهات إدارة الطلاب والصفوف
- دعم اللغة العربية والفرنسية

---

**ملاحظة:** هذا النظام في مرحلة التطوير المستمر. المزيد من الميزات ستتم إضافتها في الإصدارات القادمة.
