# نافذة تسجيل الدخول
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import os

from config.app_config import AppConfig, LanguageConfig
from config.database_config import Database
from utils.security import SecurityManager, session_manager

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, parent, on_success_callback):
        self.parent = parent
        self.on_success_callback = on_success_callback
        self.window = None
        self.current_language = AppConfig.DEFAULT_LANGUAGE
        
        # متغيرات النموذج
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()
        
        # قاعدة البيانات
        self.db = Database()
    
    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.window = tk.Toplevel(self.parent)
        self.setup_window()
        self.create_widgets()
        self.center_window()
        
        # التركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title(f"{AppConfig.APP_NAME} - تسجيل الدخول")
        self.window.geometry("400x500")
        self.window.resizable(False, False)
        self.window.configure(bg=AppConfig.COLORS['background'])
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # معالج إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شعار التطبيق
        self.create_logo(main_frame)
        
        # عنوان التطبيق
        title_label = ttk.Label(main_frame, 
                               text=AppConfig.APP_NAME,
                               style='Title.TLabel')
        title_label.pack(pady=(0, 10))
        
        # عنوان فرعي
        subtitle_label = ttk.Label(main_frame,
                                  text="نظام شامل لإدارة المدارس",
                                  style='Heading.TLabel')
        subtitle_label.pack(pady=(0, 30))
        
        # نموذج تسجيل الدخول
        self.create_login_form(main_frame)
        
        # أزرار إضافية
        self.create_additional_buttons(main_frame)
        
        # معلومات الإصدار
        version_label = ttk.Label(main_frame,
                                 text=f"الإصدار {AppConfig.APP_VERSION}",
                                 font=AppConfig.FONTS['small'])
        version_label.pack(side=tk.BOTTOM, pady=(20, 0))
    
    def create_logo(self, parent):
        """إنشاء شعار التطبيق"""
        try:
            # محاولة تحميل شعار من ملف
            logo_path = os.path.join(AppConfig.IMAGES_DIR, 'logo.png')
            if os.path.exists(logo_path):
                image = Image.open(logo_path)
                image = image.resize((80, 80), Image.Resampling.LANCZOS)
                self.logo_image = ImageTk.PhotoImage(image)
                
                logo_label = ttk.Label(parent, image=self.logo_image)
                logo_label.pack(pady=(0, 20))
            else:
                # شعار نصي بديل
                logo_label = ttk.Label(parent,
                                      text="🏫",
                                      font=('Arial', 48))
                logo_label.pack(pady=(0, 20))
                
        except Exception as e:
            # شعار نصي في حالة الخطأ
            logo_label = ttk.Label(parent,
                                  text="🏫",
                                  font=('Arial', 48))
            logo_label.pack(pady=(0, 20))
    
    def create_login_form(self, parent):
        """إنشاء نموذج تسجيل الدخول"""
        # إطار النموذج
        form_frame = ttk.LabelFrame(parent, text="تسجيل الدخول", padding="20")
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # اسم المستخدم
        ttk.Label(form_frame, text="اسم المستخدم:").pack(anchor=tk.W, pady=(0, 5))
        self.username_entry = ttk.Entry(form_frame, 
                                       textvariable=self.username_var,
                                       font=AppConfig.FONTS['default'],
                                       width=30)
        self.username_entry.pack(fill=tk.X, pady=(0, 15))
        
        # كلمة المرور
        ttk.Label(form_frame, text="كلمة المرور:").pack(anchor=tk.W, pady=(0, 5))
        self.password_entry = ttk.Entry(form_frame,
                                       textvariable=self.password_var,
                                       show="*",
                                       font=AppConfig.FONTS['default'],
                                       width=30)
        self.password_entry.pack(fill=tk.X, pady=(0, 15))
        
        # تذكرني
        remember_check = ttk.Checkbutton(form_frame,
                                        text="تذكرني",
                                        variable=self.remember_var)
        remember_check.pack(anchor=tk.W, pady=(0, 20))
        
        # زر تسجيل الدخول
        login_btn = ttk.Button(form_frame,
                              text="تسجيل الدخول",
                              command=self.login,
                              style='Accent.TButton')
        login_btn.pack(fill=tk.X, pady=(0, 10))
        
        # ربط مفتاح Enter بتسجيل الدخول
        self.window.bind('<Return>', lambda e: self.login())
        
        # رابط نسيت كلمة المرور
        forgot_btn = ttk.Button(form_frame,
                               text="نسيت كلمة المرور؟",
                               command=self.forgot_password,
                               style='Link.TButton')
        forgot_btn.pack(pady=(5, 0))
    
    def create_additional_buttons(self, parent):
        """إنشاء أزرار إضافية"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(0, 20))
        
        # زر تغيير اللغة
        language_btn = ttk.Button(buttons_frame,
                                 text="العربية | Français",
                                 command=self.toggle_language)
        language_btn.pack(side=tk.LEFT)
        
        # زر الإعدادات
        settings_btn = ttk.Button(buttons_frame,
                                 text="إعدادات قاعدة البيانات",
                                 command=self.show_db_settings)
        settings_btn.pack(side=tk.RIGHT)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        # التحقق من صحة البيانات
        if not username:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return
        
        if not password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return
        
        # التحقق من قفل الحساب
        is_locked, remaining_time = session_manager.is_account_locked(username)
        if is_locked:
            minutes = int(remaining_time.total_seconds() / 60)
            messagebox.showerror("حساب مقفل", 
                               f"الحساب مقفل لمدة {minutes} دقيقة بسبب محاولات دخول خاطئة متكررة")
            return
        
        # محاولة تسجيل الدخول
        try:
            if not self.db.connect():
                messagebox.showerror("خطأ", "لا يمكن الاتصال بقاعدة البيانات")
                return
            
            # البحث عن المستخدم
            query = """
                SELECT id, username, password_hash, user_type, full_name, email, is_active
                FROM users 
                WHERE username = %s AND is_active = TRUE
            """
            
            result = self.db.execute_query(query, (username,))
            
            if not result:
                session_manager.record_login_attempt(username, False)
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
                return
            
            user_data = result[0]
            
            # التحقق من كلمة المرور
            if not SecurityManager.verify_password(password, user_data['password_hash']):
                session_manager.record_login_attempt(username, False)
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
                return
            
            # تسجيل محاولة دخول ناجحة
            session_manager.record_login_attempt(username, True)
            
            # إغلاق النافذة وتمرير بيانات المستخدم
            self.window.destroy()
            self.on_success_callback(user_data)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")
        finally:
            self.db.disconnect()
    
    def forgot_password(self):
        """نسيت كلمة المرور"""
        messagebox.showinfo("نسيت كلمة المرور", 
                           "يرجى الاتصال بمدير النظام لإعادة تعيين كلمة المرور")
    
    def toggle_language(self):
        """تبديل اللغة"""
        if self.current_language == 'ar':
            self.current_language = 'fr'
        else:
            self.current_language = 'ar'
        
        # إعادة إنشاء الواجهة باللغة الجديدة
        self.update_language()
    
    def update_language(self):
        """تحديث لغة الواجهة"""
        # هذه الدالة ستحتوي على تحديث النصوص حسب اللغة المختارة
        pass
    
    def show_db_settings(self):
        """عرض إعدادات قاعدة البيانات"""
        messagebox.showinfo("إعدادات قاعدة البيانات",
                           "يمكن تعديل إعدادات قاعدة البيانات من ملف config/database_config.py")
    
    def on_close(self):
        """معالج إغلاق النافذة"""
        self.parent.quit()
    
    def destroy(self):
        """تدمير النافذة"""
        if self.window:
            self.window.destroy()
