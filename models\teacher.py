# نموذج بيانات المعلم
from datetime import datetime, date
from config.database_config import Database
from utils.security import SecurityManager

class Teacher:
    """نموذج بيانات المعلم"""
    
    def __init__(self):
        self.db = Database()
    
    def add_teacher(self, teacher_data):
        """إضافة معلم جديد"""
        try:
            if not self.db.connect():
                return False, "خطأ في الاتصال بقاعدة البيانات"
            
            # التحقق من عدم تكرار رقم المعلم
            if self.teacher_id_exists(teacher_data['teacher_id']):
                return False, "رقم المعلم موجود مسبقاً"
            
            # إنشاء حساب مستخدم للمعلم
            user_data = {
                'username': teacher_data['teacher_id'],
                'password_hash': SecurityManager.hash_password(teacher_data.get('password', '123456')),
                'user_type': 'teacher',
                'full_name': teacher_data['full_name'],
                'email': teacher_data.get('email', ''),
                'phone': teacher_data.get('phone', ''),
                'address': teacher_data.get('address', '')
            }
            
            # إدراج بيانات المستخدم
            user_query = """
                INSERT INTO users (username, password_hash, user_type, full_name, email, phone, address)
                VALUES (%(username)s, %(password_hash)s, %(user_type)s, %(full_name)s, %(email)s, %(phone)s, %(address)s)
            """
            
            if not self.db.execute_update(user_query, user_data):
                return False, "خطأ في إنشاء حساب المستخدم"
            
            user_id = self.db.get_last_insert_id()
            
            # إدراج بيانات المعلم
            teacher_query = """
                INSERT INTO teachers (user_id, teacher_id, specialization, qualification, 
                                    experience_years, hire_date, salary)
                VALUES (%(user_id)s, %(teacher_id)s, %(specialization)s, %(qualification)s,
                        %(experience_years)s, %(hire_date)s, %(salary)s)
            """
            
            teacher_params = {
                'user_id': user_id,
                'teacher_id': teacher_data['teacher_id'],
                'specialization': teacher_data.get('specialization', ''),
                'qualification': teacher_data.get('qualification', ''),
                'experience_years': teacher_data.get('experience_years', 0),
                'hire_date': teacher_data.get('hire_date', date.today()),
                'salary': teacher_data.get('salary', 0.0)
            }
            
            if self.db.execute_update(teacher_query, teacher_params):
                return True, "تم إضافة المعلم بنجاح"
            else:
                # حذف المستخدم في حالة فشل إضافة المعلم
                self.db.execute_update("DELETE FROM users WHERE id = %s", (user_id,))
                return False, "خطأ في إضافة بيانات المعلم"
                
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
        finally:
            self.db.disconnect()
    
    def update_teacher(self, teacher_id, teacher_data):
        """تحديث بيانات المعلم"""
        try:
            if not self.db.connect():
                return False, "خطأ في الاتصال بقاعدة البيانات"
            
            # الحصول على معرف المستخدم
            user_id_query = "SELECT user_id FROM teachers WHERE teacher_id = %s"
            result = self.db.execute_query(user_id_query, (teacher_id,))
            
            if not result:
                return False, "المعلم غير موجود"
            
            user_id = result[0]['user_id']
            
            # تحديث بيانات المستخدم
            user_query = """
                UPDATE users SET full_name = %(full_name)s, email = %(email)s, 
                               phone = %(phone)s, address = %(address)s
                WHERE id = %(user_id)s
            """
            
            user_params = {
                'user_id': user_id,
                'full_name': teacher_data['full_name'],
                'email': teacher_data.get('email', ''),
                'phone': teacher_data.get('phone', ''),
                'address': teacher_data.get('address', '')
            }
            
            if not self.db.execute_update(user_query, user_params):
                return False, "خطأ في تحديث بيانات المستخدم"
            
            # تحديث بيانات المعلم
            teacher_query = """
                UPDATE teachers SET specialization = %(specialization)s, qualification = %(qualification)s,
                                  experience_years = %(experience_years)s, hire_date = %(hire_date)s,
                                  salary = %(salary)s
                WHERE teacher_id = %(teacher_id)s
            """
            
            teacher_params = {
                'teacher_id': teacher_id,
                'specialization': teacher_data.get('specialization', ''),
                'qualification': teacher_data.get('qualification', ''),
                'experience_years': teacher_data.get('experience_years', 0),
                'hire_date': teacher_data.get('hire_date'),
                'salary': teacher_data.get('salary', 0.0)
            }
            
            if self.db.execute_update(teacher_query, teacher_params):
                return True, "تم تحديث بيانات المعلم بنجاح"
            else:
                return False, "خطأ في تحديث بيانات المعلم"
                
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
        finally:
            self.db.disconnect()
    
    def delete_teacher(self, teacher_id):
        """حذف معلم"""
        try:
            if not self.db.connect():
                return False, "خطأ في الاتصال بقاعدة البيانات"
            
            # التحقق من وجود صفوف مرتبطة بالمعلم
            classes_query = "SELECT COUNT(*) as count FROM classes WHERE class_teacher_id = (SELECT id FROM teachers WHERE teacher_id = %s)"
            result = self.db.execute_query(classes_query, (teacher_id,))
            
            if result and result[0]['count'] > 0:
                return False, "لا يمكن حذف المعلم لوجود صفوف مرتبطة به"
            
            # حذف المعلم (سيتم حذف المستخدم تلقائياً بسبب CASCADE)
            delete_query = "DELETE FROM teachers WHERE teacher_id = %s"
            
            if self.db.execute_update(delete_query, (teacher_id,)):
                return True, "تم حذف المعلم بنجاح"
            else:
                return False, "خطأ في حذف المعلم"
                
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
        finally:
            self.db.disconnect()
    
    def get_teacher_by_id(self, teacher_id):
        """الحصول على بيانات معلم بالرقم"""
        try:
            if not self.db.connect():
                return None
            
            query = """
                SELECT t.*, u.full_name, u.email, u.phone, u.address, u.is_active,
                       (SELECT COUNT(*) FROM classes WHERE class_teacher_id = t.id) as classes_count
                FROM teachers t
                JOIN users u ON t.user_id = u.id
                WHERE t.teacher_id = %s
            """
            
            result = self.db.execute_query(query, (teacher_id,))
            return result[0] if result else None
            
        except Exception as e:
            print(f"خطأ في الحصول على بيانات المعلم: {e}")
            return None
        finally:
            self.db.disconnect()
    
    def get_all_teachers(self, status='active'):
        """الحصول على جميع المعلمين"""
        try:
            if not self.db.connect():
                return []
            
            query = """
                SELECT t.*, u.full_name, u.email, u.phone, u.is_active,
                       (SELECT COUNT(*) FROM classes WHERE class_teacher_id = t.id) as classes_count
                FROM teachers t
                JOIN users u ON t.user_id = u.id
                WHERE t.status = %s
                ORDER BY u.full_name
            """
            
            return self.db.execute_query(query, (status,)) or []
            
        except Exception as e:
            print(f"خطأ في الحصول على قائمة المعلمين: {e}")
            return []
        finally:
            self.db.disconnect()
    
    def search_teachers(self, search_term):
        """البحث في المعلمين"""
        try:
            if not self.db.connect():
                return []
            
            query = """
                SELECT t.*, u.full_name, u.email, u.phone
                FROM teachers t
                JOIN users u ON t.user_id = u.id
                WHERE (u.full_name LIKE %s OR t.teacher_id LIKE %s OR t.specialization LIKE %s)
                AND t.status = 'active'
                ORDER BY u.full_name
            """
            
            search_pattern = f"%{search_term}%"
            return self.db.execute_query(query, (search_pattern, search_pattern, search_pattern)) or []
            
        except Exception as e:
            print(f"خطأ في البحث عن المعلمين: {e}")
            return []
        finally:
            self.db.disconnect()
    
    def teacher_id_exists(self, teacher_id):
        """التحقق من وجود رقم المعلم"""
        try:
            if not self.db.connect():
                return False
            
            query = "SELECT COUNT(*) as count FROM teachers WHERE teacher_id = %s"
            result = self.db.execute_query(query, (teacher_id,))
            
            return result[0]['count'] > 0 if result else False
            
        except Exception as e:
            print(f"خطأ في التحقق من رقم المعلم: {e}")
            return False
        finally:
            self.db.disconnect()
    
    def get_teacher_statistics(self):
        """الحصول على إحصائيات المعلمين"""
        try:
            if not self.db.connect():
                return {}
            
            stats = {}
            
            # إجمالي المعلمين
            total_query = "SELECT COUNT(*) as total FROM teachers WHERE status = 'active'"
            result = self.db.execute_query(total_query)
            stats['total'] = result[0]['total'] if result else 0
            
            # المعلمين حسب التخصص
            specialization_query = """
                SELECT specialization, COUNT(*) as count 
                FROM teachers 
                WHERE status = 'active' AND specialization != ''
                GROUP BY specialization
                ORDER BY count DESC
            """
            spec_result = self.db.execute_query(specialization_query)
            stats['by_specialization'] = spec_result if spec_result else []
            
            # المعلمين حسب سنوات الخبرة
            experience_query = """
                SELECT 
                    CASE 
                        WHEN experience_years < 2 THEN 'أقل من سنتين'
                        WHEN experience_years < 5 THEN '2-5 سنوات'
                        WHEN experience_years < 10 THEN '5-10 سنوات'
                        ELSE 'أكثر من 10 سنوات'
                    END as experience_range,
                    COUNT(*) as count
                FROM teachers 
                WHERE status = 'active'
                GROUP BY experience_range
            """
            exp_result = self.db.execute_query(experience_query)
            stats['by_experience'] = exp_result if exp_result else []
            
            # المعلمين بدون صفوف
            no_class_query = """
                SELECT COUNT(*) as count 
                FROM teachers t
                WHERE t.status = 'active' 
                AND t.id NOT IN (SELECT DISTINCT class_teacher_id FROM classes WHERE class_teacher_id IS NOT NULL)
            """
            no_class_result = self.db.execute_query(no_class_query)
            stats['without_classes'] = no_class_result[0]['count'] if no_class_result else 0
            
            return stats
            
        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات المعلمين: {e}")
            return {}
        finally:
            self.db.disconnect()
    
    def get_teacher_subjects(self, teacher_id):
        """الحصول على المواد التي يدرسها المعلم"""
        try:
            if not self.db.connect():
                return []
            
            query = """
                SELECT ts.*, s.subject_name, s.subject_code, c.class_name, c.section, c.grade_level
                FROM teacher_subjects ts
                JOIN subjects s ON ts.subject_id = s.id
                JOIN classes c ON ts.class_id = c.id
                JOIN teachers t ON ts.teacher_id = t.id
                WHERE t.teacher_id = %s
                ORDER BY c.grade_level, c.class_name, s.subject_name
            """
            
            return self.db.execute_query(query, (teacher_id,)) or []
            
        except Exception as e:
            print(f"خطأ في الحصول على مواد المعلم: {e}")
            return []
        finally:
            self.db.disconnect()
    
    def assign_subject_to_teacher(self, teacher_id, subject_id, class_id, academic_year):
        """تعيين مادة لمعلم في صف معين"""
        try:
            if not self.db.connect():
                return False, "خطأ في الاتصال بقاعدة البيانات"
            
            # الحصول على معرف المعلم الداخلي
            teacher_query = "SELECT id FROM teachers WHERE teacher_id = %s"
            teacher_result = self.db.execute_query(teacher_query, (teacher_id,))
            
            if not teacher_result:
                return False, "المعلم غير موجود"
            
            internal_teacher_id = teacher_result[0]['id']
            
            # التحقق من عدم وجود تعيين مسبق
            check_query = """
                SELECT COUNT(*) as count 
                FROM teacher_subjects 
                WHERE teacher_id = %s AND subject_id = %s AND class_id = %s AND academic_year = %s
            """
            
            check_result = self.db.execute_query(check_query, (internal_teacher_id, subject_id, class_id, academic_year))
            
            if check_result and check_result[0]['count'] > 0:
                return False, "المعلم مُعيَّن لهذه المادة في هذا الصف مسبقاً"
            
            # إدراج التعيين الجديد
            insert_query = """
                INSERT INTO teacher_subjects (teacher_id, subject_id, class_id, academic_year)
                VALUES (%s, %s, %s, %s)
            """
            
            if self.db.execute_update(insert_query, (internal_teacher_id, subject_id, class_id, academic_year)):
                return True, "تم تعيين المادة للمعلم بنجاح"
            else:
                return False, "خطأ في تعيين المادة"
                
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
        finally:
            self.db.disconnect()
